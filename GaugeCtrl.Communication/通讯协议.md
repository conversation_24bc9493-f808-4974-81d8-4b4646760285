以下是更新后的包含 `ERR_RESP_CMD` 协议的完整 Markdown 文档：

# 多通道数字采集项目通讯协议设计

2025.7.5

| 版本  | 作者  | 内容   | 日期       |
|-----|-----|------|----------|
| 0.1 | 陆春伟 | 初始版本 | 2025-7-5 |
|     |     |      |          |
|     |     |      |          |
|     |     |      |          |

## 1. 通讯协议设计原则

多通道数字采集系统通讯协议的制定(异于通用协议)，是为了更加方便数字采集系统、上位机、外接设备之间的通讯连接控制，因此该协议的设计必须遵循以下原则：

- 一般适用性：协议必须满足现有多通道数字采集系统的所有一般功能；
- 可扩展性：对于特殊功能需求，可以快速扩展协议实现相关功能；
- 设备之间弱逻辑关系：设备之间逻辑关系简单，逻辑关系复杂不利于问题的调试、追溯；
- 可配置性：通过代码配置重新编译之后，能满足同一功能的不同实现需求；
- 可追溯性：完整的日志系统，可追溯特定时间内的所有操作、故障信息等，用于定位分析问题；
- 系统错误自纠能力：由于嵌入式系统的数据传输、运算能力的限制以及运行环境稳定性问题，数据丢失在所难免；但是系统要有自动纠正能力，保证系统的功能性、稳定性。

## 2. 系统控制板基本功能

多通道数字采集系统控制板基本功能基本上可概括为以下几项：

- 采集模块：多达8个通道的数字化采集，主要涉及单端模拟输入；
- 信号处理模块：对采集信号进行处理(离散、滤波、信号对齐等)，并通过不同的方式上传至PC(USB3.0 & USB2.0)；
- 参数存储及日志保存：参数存储，日志信息存储；
- 数据传输：USB3.0总线：用于传输未经处理离散化的脉冲数据；USB2.0总线：用于传输提取参数后的数据；
- 参数配置：用于配置设备各类控制参数以及系统参数(USB2.0总线)；
- BOOT升级：通过BOOT程序，主机可实现多通道采集设备控制板的远程升级，程序更新至最新版本。

## 3. 名词定义

- MASTER：上位机控制组
- LOCAL：本地多通道数字采集设备控制板(PIC32MZ2048EFH144)

## 4. 地址分配

| 模块     | 地址范围      | 最大数量 | 说明      |
|--------|-----------|------|---------|
| MASTER | 0xC8(200) | 1    | 上位机控制组  |
| LOCAL  | 0xC9(201) | 1    | 采集设备控制板 |
|        |           |      |         |
|        |           |      |         |
|        |           |      |         |
|        |           |      |         |
|        |           |      |         |
|        |           |      |         |

## 5. 协议帧格式定义

| 字节          | 名称         | 数据类型        | 含义               | 取值范围 | 备注 |
|-------------|------------|-------------|------------------|------|----|
| 0           | Head0      | uint8_t     | 帧头1              | 0x55 |    |
| 1           | Head1      | uint8_t     | 帧头2              | 0xAA |    |
| 2           | DstAddr    | uint8_t     | 目的地址             |      |    |
| 3           | SrcAddr    | uint8_t     | 信源地址             |      |    |
| 4-5         | DataSize   | uint16_t    | 实际数据长度           |      |    |
| 6+0         | Data(字节对齐) | uint8_t [N] | 负载数据，其中第一个字节为命令码 |      |    |
| …           |            |             |                  |      |    |
| 6+(N-1)     |            |             |                  |      |    |
| 6+(N)-7+(N) | Crc        | uint16_t    | crc数据校验          |      |    |
| 8+(N)       | Tail0      | uint8_t     | 帧尾1              | 0xA5 |    |
| 9+(N)       | Tail1      | uint8_t     | 帧尾2              | 0x5A |    |

PS：上述协议为USB3.0总线 & USB2.0总线的通讯协议，两个总线传输数据存在区别，不可混为一谈。

## 6. 协议实现

### 6.1令牌消息

USB2.0总线，用于采集设备控制板与上位机之间的通讯，用于确认心跳（此处暂未使用）。

| 协议  | Rev | Rev  | Rev |
|-----|-----|------|-----|
| 协议号 | Rev | Rev  | Rev |
| 说明  | Rev | Rev  | Rev |
| 字节  | 含义  | 数据类型 | 备注  |

### 6.2控制类消息

#### 用于USB2.0 & US3.0B总线，为采集设备控制板与上位机之间的通用控制协议。

| 协议  | FW_UPDATE_CMD | FW_UPDATE_CMD | FW_UPDATE_CMD                                                                                                                                                                                                           |
|-----|---------------|---------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 协议号 | 0x01-0x07     | 0x01-0x07     | 0x01-0x07                                                                                                                                                                                                               |
| 说明  | 固件升级等指令       | 固件升级等指令       | 固件升级等指令                                                                                                                                                                                                                 |
| 字节  | 含义            | 数据类型          | 备注                                                                                                                                                                                                                      |
| 0   | 固件升级等一系列指令码   | CHAR          | 0x01：固件升级请求 FW_UPDATE_REQ<br/>0x02：升级请求应答 FW_UPDATE_ACK<br/>0x03：固件升级数据 FW_UPDATE_DATA<br/>0x04：固件升级错误 FW_UPDATE_ERROR<br/>0x05：固件升级成功 FW_UPDATE_OK<br/>0x06：固件版本请求 FW_UPDATE_VERREQ<br/>0x07：固件版本应答 FW_UPDATE_VERREPLY |

| 协议   | FW_HANDLE_CMD                     | FW_HANDLE_CMD | FW_HANDLE_CMD                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
|------|-----------------------------------|---------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 协议号  | 0x08-0x0A                         | 0x08-0x0A     | 0x08-0x0A                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| 说明   | 信息处理等一系列指令                        | 信息处理等一系列指令    | 信息处理等一系列指令                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 字节   | 含义                                | 数据类型          | 备注                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| 0    | 信息处理等一系列指令码                       | CHAR          | 0x08：固件参数设置 FW_HANDLE_PARA_INFO_SET                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| 1    | 命令类型                              | CHAR          | 0x08：固件参数设置 FW_HANDLE_PARA_INFO_SET<br/>0x01-系统参数(增益调节、基线调节)<br/>0x02-滤波参数(滤波算法涉及到的系数)<br/>0x03-信号时延参数<br/>0x04-数据回传类型参数<br/>0x05-触发阈值参数<br/>0x06-信号提取参数<br/>0x07-系统控制(示波器功能)<br/>0xAA-参数恢复出厂设置(无后续说明信息)                                                                                                                                                                                                                                                                                                                                                                               |
| 2    | 命令设置                              | CHAR          | 仅限于FW_HANDLE_PARA_INFO_SET命令<br/>针对0x01-0x06：所有类型参数(命令类型)<br/>0x01-单个采集设备，0x02-多个采集设备                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 3-8  | 系统参数设置<br/>PS：与其他类型参数设置不可同时使用     | UINT16        | 系统参数设置，当SET命令为系统参数设置（0x01）时方可生效<br/>通道选择：3-4字节（UINT16），0x00-0x07代表1-8号通道<br/>增益调节：5-6字节（UINT16），单位mv<br/>基线调节：7-10字节（UINT32），单位mv                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| 3-24 | 滤波参数设置<br/>PS：与其他类型参数设置不可同时使用     | UINT16+UINT32 | 滤波参数设置，当SET命令为滤波参数设置（0x02）时方可生效<br/>算法激活类型选择：3-4字节（UINT16）<br/>0x01-低通滤波，0x02-高通滤波，0x03-滑动平均<br/>截止频率(针对低通滤波)：5-8字节（UINT32），单位KHz<br/>采样周期(针对低通滤波)：9-12字节（UINT32），单位ms<br/>截止频率(针对高通滤波)：13-16字节（UINT32），单位KHz<br/>采样周期(针对高通滤波)：17-20字节（UINT32），单位ms<br/>窗口宽度(针对滑动平均)：21-24字节（UINT32）<br/>PS：若选用低通或高通滤波，针对滑动平均的窗口宽度无需设置，赋0即可，反之也是一样                                                                                                                                                                                                                                                     |
| 3-6  | 信号时延参数设置<br/>PS：与其他类型参数设置不可同时使用   | UINT32        | 信号时延参数设置，当SET命令为信号时延参数设置（0x03）时方可生效<br/>信号延迟时长△T：3-6字节（UINT32），单位ms<br/>PS：延迟时长△T为不同信号之间延迟的最大时长，或者说不同信号之间都按照这个时长来进行同步(需确认)                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 3-4  | 数据回传类型参数设置<br/>PS：与其他类型参数设置不可同时使用 | UINT16        | 数据回传类型参数设置，当SET命令为数据回传类型参数设置（0x04）时方可生效<br/>数据上传类型：3-4字节 （UINT16）<br/>0x01-原始数据，0x02-基线去除后的数据，0x03-位宽扩展后的数据<br/>PS：0x01-0x03的类型对应的数据通过USB3.0上传                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| 3-6  | 触发阈值参数设置<br/>PS：与其他类型参数设置不可同时使用   | UINT32        | 触发阈值参数设置，当SET命令为触发阈值参数设置（0x05）时方可生效<br/>触发阈值机制操作：3-4字节(UINT16)，0x01-启动，0x02-不启动<br/>位宽扩展触发阈值：5-6字节（UINT32），单位mv                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 3-4  | 信号提取参数设置<br/>PS：与其他类型参数设置不可同时使用   | UINT16        | 信号提取参数设置，当SET命令为信号提取参数设置（0x06）时方可生效<br/>提取参数上传操作：3-4字节（UINT16）<br/>0x01-提取参数上传，0x00-提取参数不上传<br/>PS：该类型数据通过USB2.0上传                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| 3-20 | 系统控制设置<br/>PS：与其他类型参数设置不可同时使用     | UINT16        | 系统参数设置，当SET命令为系统控制设置（0x07）时方可生效<br/>示波器控制：3-4字节，向该参数写入1，会根据当前示波器通道的配置返回波形数据<br/>示波器通道0：5-6字节，示波器通道0的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道1：7-8字节，示波器通道1的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道2：9-10字节，示波器通道2的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道3：11-12字节，示波器通道3的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道4：13-14字节，示波器通道4的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道5：15-16字节，示波器通道5的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道6：17-18字节，示波器通道6的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形<br/>示波器通道7：19-20字节，示波器通道7的参数地址映射，在该参数填入需要监控的参数地址，可通过上位机反馈参数波形 |

| 协议        | FW_INFO_RESP_CMD                  | FW_INFO_RESP_CMD | FW_INFO_RESP_CMD                                                                                                                                                                                                                                                                                                                                                                    |
|-----------|-----------------------------------|------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 协议号       | 0x0B                              | 0x0B             | 0x0B                                                                                                                                                                                                                                                                                                                                                                                |
| 说明        | 设备信息上报                            | 设备信息上报           | 设备信息上报                                                                                                                                                                                                                                                                                                                                                                              |
| 字节        | 含义                                | 数据类型             | 备注                                                                                                                                                                                                                                                                                                                                                                                  |
| 0         | 设备信息上报指令码                         | CHAR             | 0x0B：设备信息上报 FW_INFO_RESP_CMD                                                                                                                                                                                                                                                                                                                                                        |
| 1         | 采集设备编号                            | CHAR             | 0x01-单个采集设备<br/>0x02-所有采集设备                                                                                                                                                                                                                                                                                                                                                         |
| 2         | 参数类型                              | CHAR             | 0x01-系统参数(增益调节、基线调节)<br/>0x02-滤波参数(滤波算法涉及到的系数)<br/>0x03-信号时延参数<br/>0x04-数据回传类型参数<br/>0x05-触发阈值参数<br/>0x06-信号提取参数<br/>0x0A-示波器曲线数据<br/>0x0B-错误曲线数据                                                                                                                                                                                                                                   |
| 3-10      | 系统参数上报<br/>PS：与其他类型参数不可同时上报       | UINT16+UINT32    | 系统参数上报，当参数类型为系统参数（0x01）时方可生效<br/>上报通道：3-4字节（UINT16）<br/>增益调节：5-6字节（UINT16）<br/>基线调节：7-10字节（UINT32）                                                                                                                                                                                                                                                                                  |
| 3-22      | 滤波参数上报<br/>PS：与其他类型参数设置不可同时使用     | UINT16+UINT32    | 滤波参数上报，当参数类型为滤波参数（0x02）时方可生效<br/>截止频率(针对低通滤波)：3-6字节（UINT32）<br/>采样周期(针对低通滤波)：7-10字节（UINT32）<br/>截止频率(针对高通滤波)：11-14字节（UINT32）<br/>采样周期(针对高通滤波)：15-18字节（UINT32）<br/>窗口宽度(针对滑动平均)：19-22字节（UINT32）                                                                                                                                                                                      |
| 3-6       | 信号时延参数上报<br/>PS：与其他类型参数设置不可同时使用   | UINT32           | 信号时延参数上报，当参数类型为信号时延参数（0x03）时方可生效<br/>信号延迟时长△T：3-6字节（UINT32），单位ms                                                                                                                                                                                                                                                                                                                    |
| 3-20/3-36 | 数据回传类型参数上报<br/>PS：与其他类型参数设置不可同时使用 | UINT16(UINT32)   | 数据回传类型参数上报，当参数类型为数据回传类型参数（0x04）时方可生效<br/>数据上传类型：3-4字节 （UINT16）<br/>0x01-原始数据，0x02-基线去除后的数据(需信号对齐)，0x03-位宽扩展后的数据(需信号对齐)<br/><br/><br/>PS：0x01-0x03的类型对应的数据通过USB3.0上传<br/><br/><br/>后跟8 * 2或8*4字节数据（UINT16或UINT32，8个采集通道的数据）<br/>PS：需注意，该指令数据在开启采集之后实时上传                                                                                                                              |
| 3-6       | 触发阈值参数上报<br/>PS：与其他类型参数设置不可同时使用   | UINT32           | 触发阈值参数上报，当参数类型为触发阈值参数（0x05）时方可生效<br/>位宽扩展触发阈值：3-6字节（UINT32）                                                                                                                                                                                                                                                                                                                         |
| 3-14      | 信号提取参数上报<br/>PS：与其他类型参数设置不可同时使用   | UINT32           | 信号提取参数上报，当参数类型为信号提取参数（0x06）时方可生效<br/>信号高度H：3-6字节<br/>信号宽度W：7-10字节<br/>信号面积A：11-14字节<br/>PS：需注意，该指令数据在开启采集之后实时上传<br/>8个通道的数据一起上报                                                                                                                                                                                                                                                     |
|           | 示波器曲线数据上报                         | BYTE+INT32       | 示波器参数类型0x0A，第1字节<br/>采集数据的通道(BYTE)：第2字节，0-7代表1-8通道<br/>后跟共24字节，6个通道INT32数据<br/><br/><br/>SCOPE_ORIGINAL_DATA_TYPE = 0x0001,原始数据<br/>SCOPE_BASELINE_OPT_DATA_TYPE = 0x0002,基线数据<br/>SCOPE_BIT_WIDTH_EXPANSION_TYPE = 0x0003,位宽数据<br/>SCOPE_PARA_EXTRACT_H_TYPE = 0x0004,信号高度数据<br/>SCOPE_PARA_EXTRACT_W_TYPE = 0x0005,信号宽度数据<br/>SCOPE_PARA_EXTRACT_A_TYPE = 0x0006,信号面积数据<br/><br/> |
|           | 错误记录曲线上报                          | BYTE+INT16+INT32 | 错误记录曲线类型0x0B：第1字节<br/>发生错误的通道(BYTE)：第2字节，0-7代表1-8通道<br/>故障数据类型（BYTE）：第3字节 <br/>0x01-原始数据，0x02-基线去除后的数据，0x03-位宽扩展后的数据，0x04-参数提取的H数据，0x05-参数提取的W数据，0x06-参数提取的A数据<br/>最终索引：(INT16) 第4-5字节<br/>共4000个字节的数据：（1000个int32类型）<br/><br/>                                                                                                                                                     |

| 协议  | FW_AUTH_CMD | FW_AUTH_CMD | FW_AUTH_CMD |
|-----|-------------|-------------|-------------|
| 协议号 | 0x0C        | 0x0C        | 0x0C        |
| 说明  | 授权指令，用于握手连接 | 授权指令，用于握手连接 | 授权指令，用于握手连接 |