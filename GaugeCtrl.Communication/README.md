# GaugeCtrl.Communication

基于TouchSocket 3.1的多通道数字采集系统通讯库，实现了完整的协议解析和数据适配器功能。

## 功能特性

- ✅ 完整的协议帧解析和构建
- ✅ 多种数据适配器支持（协议数据、示波器数据、参数数据）
- ✅ 包序列化模式实现
- ✅ CRC-16校验支持
- ✅ 异步通讯接口
- ✅ 完整的命令构建器
- ✅ 事件驱动的数据处理
- ✅ 详细的日志记录

## 协议支持

### 帧格式
```
| 字节 | 名称     | 数据类型  | 含义         | 取值范围 |
|------|----------|-----------|--------------|----------|
| 0    | Head0    | uint8_t   | 帧头1        | 0x55     |
| 1    | Head1    | uint8_t   | 帧头2        | 0xAA     |
| 2    | DstAddr  | uint8_t   | 目的地址     |          |
| 3    | SrcAddr  | uint8_t   | 信源地址     |          |
| 4-5  | DataSize | uint16_t  | 实际数据长度 |          |
| 6+N  | Data     | uint8_t[] | 负载数据     |          |
| N+6  | Crc      | uint16_t  | CRC数据校验  |          |
| N+8  | Tail0    | uint8_t   | 帧尾1        | 0xA5     |
| N+9  | Tail1    | uint8_t   | 帧尾2        | 0x5A     |
```

### 支持的命令

#### 固件升级命令 (0x01-0x07)
- `FW_UPDATE_REQ` (0x01): 固件升级请求
- `FW_UPDATE_ACK` (0x02): 升级请求应答
- `FW_UPDATE_DATA` (0x03): 固件升级数据
- `FW_UPDATE_ERROR` (0x04): 固件升级错误
- `FW_UPDATE_OK` (0x05): 固件升级成功
- `FW_UPDATE_VERREQ` (0x06): 固件版本请求
- `FW_UPDATE_VERREPLY` (0x07): 固件版本应答

#### 参数处理命令 (0x08-0x0A)
- `FW_HANDLE_PARA_INFO_SET` (0x08): 固件参数设置

#### 设备信息命令 (0x0B)
- `FW_INFO_RESP_CMD` (0x0B): 设备信息上报

#### 控制命令 (0x0C-0x12)
- `FW_AUTH_CMD` (0x0C): 授权指令
- `FW_LOG_CMD` (0x0D): 日志指令
- `ERRRESET_CMD` (0x0E): 错误复位
- `ERRRECORDCLEAR_CMD` (0x0F): 错误记录清除
- `GETERRRECORD_CMD` (0x10): 获取错误记录
- `DEV_CTRL_CMD` (0x11): 采集设备控制命令
- `SOFT_RST_CMD` (0x12): 采集设备软复位

## 使用示例

### 基本连接和通讯

```csharp
using GaugeCtrl.Communication;
using GaugeCtrl.Core.Models;

// 创建串口服务
var serialService = new SerialPortService();

// 订阅事件
serialService.OscilloscopeDataReceived += (data) => {
    Console.WriteLine($"示波器数据: CH1={data.Channel1Value}, CH2={data.Channel2Value}");
};

serialService.ProtocolDataReceived += (protocolInfo) => {
    Console.WriteLine($"收到协议数据: {protocolInfo.PacketType}");
    
    // 根据数据包类型处理不同数据
    switch (protocolInfo.PacketType)
    {
        case PacketType.SystemParameter:
            if (protocolInfo.SystemParameter != null)
                Console.WriteLine($"系统参数: {protocolInfo.SystemParameter.ChannelSelection}");
            break;
        case PacketType.OscilloscopeData:
            if (protocolInfo.OscilloscopeData != null)
                Console.WriteLine($"示波器: CH1={protocolInfo.OscilloscopeData.Channel1Value}");
            break;
    }
};

// 配置串口
var config = new SerialPortConfig
{
    PortName = "COM3",
    BaudRate = 115200,
    DataBits = 8,
    Parity = System.IO.Ports.Parity.None,
    StopBits = System.IO.Ports.StopBits.One
};

// 连接
if (await serialService.ConnectAsync(config))
{
    Console.WriteLine("连接成功");
    
    // 发送授权命令（使用RequestInfoBuilder方式）
    await serialService.SendAuthCommandAsync();
    
    // 启动设备采集（使用RequestInfoBuilder方式）
    await serialService.SendDeviceControlCommandAsync(DeviceControlCommand.Start);
    
    // 使用包序列化方式发送命令
    var authPackage = ProtocolCommandPackage.CreateAuthCommand();
    await serialService.SendPackageCommandAsync(authPackage);
}
```

### 包序列化使用

```csharp
// 创建示波器数据包
var oscilloscopePackage = new OscilloscopeDataPackage
{
    Channel1Value = 1000,
    Channel2Value = 2000,
    Channel3Value = 3000,
    Channel4Value = 4000,
    Channel5Value = 5000,
    Channel6Value = 6000
};

// 序列化
using var byteBlock = new ByteBlock(1024);
oscilloscopePackage.Package(ref byteBlock);
Console.WriteLine($"序列化完成，长度: {byteBlock.Length} 字节");

// 反序列化
byteBlock.SeekToStart();
var newPackage = new OscilloscopeDataPackage();
newPackage.Unpackage(ref byteBlock);
Console.WriteLine($"反序列化完成 - CH1: {newPackage.Channel1Value}");
```

### RequestInfoBuilder使用

```csharp
// 创建命令请求
var authCommand = CommandRequestInfo.CreateAuthCommand();
var deviceControlCommand = CommandRequestInfo.CreateDeviceControlCommand(DeviceControlCommand.Start);

// 直接发送RequestInfo对象
await serialService._serialClient.SendAsync(authCommand);
await serialService._serialClient.SendAsync(deviceControlCommand);
```

### 参数配置

```csharp
// 配置系统参数
var systemParam = new SystemParameter
{
    ChannelSelection = 0x01,
    GainAdjustment = 100,
    BaselineAdjustment = 2048
};
await serialService.SendSystemParameterSetCommandAsync(systemParam);

// 配置滤波参数
var filterParam = new FilterParameter
{
    AlgorithmType = FilterType.LowPass,
    CutoffFrequency = 1000,
    SamplingPeriod = 100,
    WindowWidth = 0
};
await serialService.SendFilterParameterSetCommandAsync(filterParam);
```

### 数据请求

```csharp
// 请求系统参数
await serialService.SendDeviceInfoRequestAsync(0x01, ParameterType.SystemParameter);

// 请求示波器数据
await serialService.SendDeviceInfoRequestAsync(0x01, ParameterType.OscilloscopeData);

// 请求错误记录
await serialService.SendGetErrorRecordCommandAsync();
```

## 数据适配器

### UnifiedProtocolDataAdapter
统一协议数据适配器，处理所有类型的协议数据包：
- 协议帧解析
- 示波器数据包（6个通道的INT32数据）
- 参数设置和上报数据包
- 支持`IRequestInfoBuilder`接口，可以直接发送请求对象

**注意**：TouchSocket中一个连接只能使用一个数据适配器，因此我们使用统一适配器来处理所有数据类型。

## 包序列化模式

### 支持的包模型
- `SystemParameterPackage`: 系统参数包
- `FilterParameterPackage`: 滤波参数包
- `OscilloscopeDataPackage`: 示波器数据包
- `ProtocolCommandPackage`: 协议命令包

### 优势
- 比传统序列化快30%以上
- 数据量最少
- 支持直接发送包对象

## 测试

运行协议测试：

```csharp
using GaugeCtrl.Communication.Tests;

// 运行所有测试
ProtocolTests.RunAllTests();

// 运行示例应用
await ConsoleTestApp.RunAsync();
```

## 项目结构

```
GaugeCtrl.Communication/
├── Adapters/                    # 数据适配器
│   └── UnifiedProtocolDataAdapter.cs
├── RequestInfos/                # 请求信息类
│   ├── UnifiedProtocolRequestInfo.cs
│   └── CommandRequestInfo.cs
├── Utils/                       # 工具类
│   └── CrcCalculator.cs
├── Examples/                    # 示例代码
│   ├── ProtocolUsageExample.cs
│   └── ConsoleTestApp.cs
├── Tests/                       # 测试代码
│   └── ProtocolTests.cs
├── SerialPortService.cs         # 主要服务类
├── ProtocolFrameParser.cs       # 协议帧解析器
└── CommandBuilder.cs            # 命令构建器
```

## 依赖项

- TouchSocket 3.1.11
- TouchSocket.SerialPorts 3.1.11
- NLog 6.0.1
- .NET 8.0

## 注意事项

1. 确保串口配置正确（波特率、数据位、校验位、停止位）
2. 发送命令后适当延时等待响应
3. 监听相应的事件来处理接收到的数据
4. 使用CRC校验确保数据完整性
5. 适当的错误处理和日志记录

## 更新日志

### v1.0.0
- 实现完整的TouchSocket 3.1协议支持
- 支持多种数据适配器
- 包序列化模式实现
- 完整的命令构建和解析功能