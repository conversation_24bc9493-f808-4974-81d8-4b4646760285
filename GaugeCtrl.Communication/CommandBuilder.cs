using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Models;
using NLog;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 命令构建器
    /// 用于构建各种协议命令
    /// </summary>
    public static class CommandBuilder
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 构建授权命令
        /// </summary>
        /// <returns>授权命令帧</returns>
        public static byte[] BuildAuthCommand()
        {
            Logger.Debug("构建授权命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_AUTH_CMD);
        }

        /// <summary>
        /// 构建设备控制命令
        /// </summary>
        /// <param name="command">控制命令</param>
        /// <returns>设备控制命令帧</returns>
        public static byte[] BuildDeviceControlCommand(DeviceControlCommand command)
        {
            var commandData = new byte[] { (byte)command };
            Logger.Debug($"构建设备控制命令: {command}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.DEV_CTRL_CMD, commandData);
        }

        /// <summary>
        /// 构建软复位命令
        /// </summary>
        /// <returns>软复位命令帧</returns>
        public static byte[] BuildSoftResetCommand()
        {
            Logger.Debug("构建软复位命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.SOFT_RST_CMD);
        }

        /// <summary>
        /// 构建错误复位命令
        /// </summary>
        /// <returns>错误复位命令帧</returns>
        public static byte[] BuildErrorResetCommand()
        {
            Logger.Debug("构建错误复位命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.ERRRESET_CMD);
        }

        /// <summary>
        /// 构建错误记录清除命令
        /// </summary>
        /// <returns>错误记录清除命令帧</returns>
        public static byte[] BuildErrorRecordClearCommand()
        {
            Logger.Debug("构建错误记录清除命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.ERRRECORDCLEAR_CMD);
        }

        /// <summary>
        /// 构建获取错误记录命令
        /// </summary>
        /// <returns>获取错误记录命令帧</returns>
        public static byte[] BuildGetErrorRecordCommand()
        {
            Logger.Debug("构建获取错误记录命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.GETERRRECORD_CMD);
        }

        /// <summary>
        /// 构建固件版本请求命令
        /// </summary>
        /// <returns>固件版本请求命令帧</returns>
        public static byte[] BuildFirmwareVersionRequestCommand()
        {
            Logger.Debug("构建固件版本请求命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_UPDATE_VERREQ);
        }

        /// <summary>
        /// 构建系统参数设置命令
        /// </summary>
        /// <param name="parameter">系统参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>系统参数设置命令帧</returns>
        public static byte[] BuildSystemParameterSetCommand(SystemParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.SystemParameter,  // 命令类型
                (byte)(isSingleDevice ? 0x01 : 0x02)  // 命令设置
            };

            // 添加系统参数数据
            commandData.AddRange(BitConverter.GetBytes(parameter.ChannelSelection));
            commandData.AddRange(BitConverter.GetBytes(parameter.GainAdjustment));
            commandData.AddRange(BitConverter.GetBytes(parameter.BaselineAdjustment));

            Logger.Debug($"构建系统参数设置命令 - 通道: {parameter.ChannelSelection}, 增益: {parameter.GainAdjustment}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建滤波参数设置命令
        /// </summary>
        /// <param name="parameter">滤波参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>滤波参数设置命令帧</returns>
        public static byte[] BuildFilterParameterSetCommand(FilterParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.FilterParameter,  // 命令类型
                (byte)(isSingleDevice ? 0x01 : 0x02)  // 命令设置
            };

            // 添加滤波参数数据 (按协议顺序)
            commandData.AddRange(BitConverter.GetBytes((ushort)parameter.AlgorithmType));        // 算法激活类型选择
            commandData.AddRange(BitConverter.GetBytes(parameter.LowPassCutoffFrequency));       // 截止频率(低通滤波)
            commandData.AddRange(BitConverter.GetBytes(parameter.LowPassSamplingPeriod));        // 采样周期(低通滤波)
            commandData.AddRange(BitConverter.GetBytes(parameter.HighPassCutoffFrequency));      // 截止频率(高通滤波)
            commandData.AddRange(BitConverter.GetBytes(parameter.HighPassSamplingPeriod));       // 采样周期(高通滤波)
            commandData.AddRange(BitConverter.GetBytes(parameter.WindowWidth));                  // 窗口宽度(滑动平均)

            Logger.Debug($"构建滤波参数设置命令 - 算法: {parameter.AlgorithmType}, 低通截止频率: {parameter.LowPassCutoffFrequency}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建时延参数设置命令
        /// </summary>
        /// <param name="parameter">时延参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>时延参数设置命令帧</returns>
        public static byte[] BuildDelayParameterSetCommand(DelayParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.DelayParameter,   // 命令类型
                (byte)(isSingleDevice ? 0x01 : 0x02) // 命令设置
            };

            // 添加时延参数数据
            commandData.AddRange(BitConverter.GetBytes(parameter.DelayTime));

            Logger.Debug($"构建时延参数设置命令 - 延迟时间: {parameter.DelayTime}ms");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建数据回传类型参数设置命令
        /// </summary>
        /// <param name="parameter">数据类型参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>数据回传类型参数设置命令帧</returns>
        public static byte[] BuildDataTypeParameterSetCommand(RespTypeParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.RespTypeParameter, // 命令类型
                (byte)(isSingleDevice ? 0x01 : 0x02)   // 命令设置
            };

            // 添加数据类型参数
            commandData.AddRange(BitConverter.GetBytes((ushort)parameter.UploadType));

            Logger.Debug($"构建数据类型参数设置命令 - 类型: {parameter.UploadType}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建触发阈值参数设置命令
        /// </summary>
        /// <param name="parameter">阈值参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>触发阈值参数设置命令帧</returns>
        public static byte[] BuildThresholdParameterSetCommand(ThresholdParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.ThresholdParameter, // 命令类型
                (byte)(isSingleDevice ? 0x01 : 0x02)    // 命令设置
            };

            // 添加阈值参数数据
            commandData.AddRange(BitConverter.GetBytes(parameter.ThresholdMechanismOperation)); // 触发阈值机制操作
            commandData.AddRange(BitConverter.GetBytes(parameter.BitWidthExpandThreshold));     // 位宽扩展触发阈值

            Logger.Debug($"构建阈值参数设置命令 - 机制操作: {parameter.ThresholdMechanismOperation}, 阈值: {parameter.BitWidthExpandThreshold}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建信号提取参数设置命令
        /// </summary>
        /// <param name="parameter">信号提取参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>信号提取参数设置命令帧</returns>
        public static byte[] BuildSignalExtractionParameterSetCommand(bool isUpload, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.ExtractionParameter, // 命令类型
                (byte)(isSingleDevice ? 0x01 : 0x02) ,    // 命令设置
                (byte)(isUpload ? 0x01 : 0x00)     // 0x01-提取参数上传，0x00-提取参数不上传
            };
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// <summary>
        /// 构建参数恢复出厂设置命令
        /// </summary>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>参数恢复出厂设置命令帧</returns>
        public static byte[] BuildFactoryResetCommand(bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                0xAA, // 参数恢复出厂设置
                (byte)(isSingleDevice ? 0x01 : 0x02)
            };

            Logger.Debug("构建参数恢复出厂设置命令");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建示波器参数设置命令
        /// </summary>
        /// <param name="parameter">示波器控制参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        /// <returns>示波器参数设置命令帧</returns>
        public static byte[] BuildOscilloscopeParameterSetCommand(OscilloscopeControlParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                0x07, // 系统控制设置
                (byte)(isSingleDevice ? 0x01 : 0x02) // 命令设置
            };

            // 添加示波器控制参数 (18字节)
            commandData.AddRange(BitConverter.GetBytes(parameter.OscilloscopeControl));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel0Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel1Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel2Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel3Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel4Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel5Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel6Address));
            commandData.AddRange(BitConverter.GetBytes(parameter.Channel7Address));

            Logger.Debug($"构建示波器参数设置命令 - 控制: {parameter.OscilloscopeControl}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_HANDLE_PARA_INFO_SET, commandData.ToArray());
        }

        /// <summary>
        /// 构建设备信息请求命令
        /// </summary>
        /// <param name="deviceNumber">设备编号 (0x01-当前设备, 0x02-所有设备)</param>
        /// <param name="parameterType">参数类型</param>
        /// <returns>设备信息请求命令帧</returns>
        public static byte[] BuildDeviceInfoRequestCommand(byte deviceNumber, ParameterType parameterType)
        {
            var commandData = new byte[] { deviceNumber, (byte)parameterType };
            Logger.Debug($"构建设备信息请求命令 - 设备: 0x{deviceNumber:X2}, 参数类型: {parameterType}");
            return ProtocolFrameParser.BuildCommandFrame(CommandCode.FW_INFO_RESP_CMD, commandData);
        }
    }
}