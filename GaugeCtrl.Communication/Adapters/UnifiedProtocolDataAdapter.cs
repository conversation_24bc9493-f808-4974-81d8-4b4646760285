using TouchSocket.Core;
using TouchSocket.SerialPorts;
using GaugeCtrl.Communication.RequestInfos;
using NLog;

namespace GaugeCtrl.Communication.Adapters
{
    /// <summary>
    /// 统一协议数据适配器
    /// 处理所有类型的协议数据包（协议帧、示波器数据、参数数据等）
    /// </summary>
    public class UnifiedProtocolDataAdapter : CustomFixedHeaderDataHandlingAdapter<UnifiedProtocolRequestInfo>
    {
        private new static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 固定包头长度 (帧头1 + 帧头2 + 目标地址 + 源地址 + 数据长度)
        /// </summary>
        public override int HeaderLength => 6;

        /// <summary>
        /// 支持发送RequestInfo对象
        /// </summary>
        public override bool CanSendRequestInfo => true;

        /// <summary>
        /// 获取新的统一协议请求信息实例
        /// </summary>
        /// <returns>新的UnifiedProtocolRequestInfo实例</returns>
        protected override UnifiedProtocolRequestInfo GetInstance()
        {
            return new UnifiedProtocolRequestInfo();
        }


    }
}