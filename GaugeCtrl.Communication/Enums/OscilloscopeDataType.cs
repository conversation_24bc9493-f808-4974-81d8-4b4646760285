namespace GaugeCtrl.Communication.Enums;

/// <summary>
/// 示波器数据类型枚举
/// </summary>
public enum OscilloscopeDataType : ushort
{
    /// <summary>
    /// 原始数据
    /// </summary>
    OriginalData = 0x0001,

    /// <summary>
    /// 基线数据
    /// </summary>
    BaselineData = 0x0002,

    /// <summary>
    /// 位宽数据
    /// </summary>
    BitWidthData = 0x0003,

    /// <summary>
    /// 信号高度数据
    /// </summary>
    SignalHeightData = 0x0004,

    /// <summary>
    /// 信号宽度数据
    /// </summary>
    SignalWidthData = 0x0005,

    /// <summary>
    /// 信号面积数据
    /// </summary>
    SignalAreaData = 0x0006
}