namespace GaugeCtrl.Communication.Enums
{
    /// <summary>
    /// 错误响应命令枚举
    /// 对应协议中的ERR_RESP_CMD命令
    /// </summary>
    public enum ErrorResponseCommand : byte
    {
        /// <summary>
        /// 未定义错误
        /// </summary>
        Undefined = 0x00,

        /// <summary>
        /// 命令格式错误
        /// </summary>
        CommandFormatError = 0x01,

        /// <summary>
        /// 参数范围错误
        /// </summary>
        ParameterRangeError = 0x02,

        /// <summary>
        /// 设备未就绪
        /// </summary>
        DeviceNotReady = 0x03,

        /// <summary>
        /// 通讯超时
        /// </summary>
        CommunicationTimeout = 0x04,

        /// <summary>
        /// CRC校验错误
        /// </summary>
        CrcError = 0x05,

        /// <summary>
        /// 数据长度错误
        /// </summary>
        DataLengthError = 0x06,

        /// <summary>
        /// 不支持的命令
        /// </summary>
        UnsupportedCommand = 0x07,

        /// <summary>
        /// 设备忙碌
        /// </summary>
        DeviceBusy = 0x08,

        /// <summary>
        /// 内存不足
        /// </summary>
        InsufficientMemory = 0x09,

        /// <summary>
        /// 硬件故障
        /// </summary>
        HardwareFailure = 0x0A,

        /// <summary>
        /// 授权失败
        /// </summary>
        AuthorizationFailed = 0x0B,

        /// <summary>
        /// 固件版本不匹配
        /// </summary>
        FirmwareVersionMismatch = 0x0C,

        /// <summary>
        /// 配置错误
        /// </summary>
        ConfigurationError = 0x0D,

        /// <summary>
        /// 系统错误
        /// </summary>
        SystemError = 0xFF
    }
}