namespace GaugeCtrl.Communication.Enums
{
    /// <summary>
    /// 参数类型枚举
    /// </summary>
    public enum ParameterType : byte
    {
        SystemParameter = 0x01,      // 系统参数(增益调节、基线调节)
        FilterParameter = 0x02,      // 滤波参数(滤波算法涉及到的系数)
        DelayParameter = 0x03,       // 信号时延参数
        RespTypeParameter = 0x04,    // 数据回传类型参数
        ThresholdParameter = 0x05,   // 触发阈值参数
        ExtractionParameter = 0x06,  // 信号提取参数
        OscilloscopeData = 0x0A,     // 示波器曲线数据
        ErrorData = 0x0B,            // 错误曲线数据
        FactoryReset = 0xAA          // 参数恢复出厂设置
    }
}