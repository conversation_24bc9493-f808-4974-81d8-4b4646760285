namespace GaugeCtrl.Communication.Enums
{
    /// <summary>
    /// 命令码枚举
    /// </summary>
    public enum CommandCode : byte
    {
        // 固件升级命令 0x01-0x07
        FW_UPDATE_REQ = 0x01,       // 固件升级请求
        FW_UPDATE_ACK = 0x02,       // 升级请求应答
        FW_UPDATE_DATA = 0x03,      // 固件升级数据
        FW_UPDATE_ERROR = 0x04,     // 固件升级错误
        FW_UPDATE_OK = 0x05,        // 固件升级成功
        FW_UPDATE_VERREQ = 0x06,    // 固件版本请求
        FW_UPDATE_VERREPLY = 0x07,  // 固件版本应答
        
        // 信息处理命令 0x08-0x0A
        FW_HANDLE_PARA_INFO_SET = 0x08,  // 固件参数设置
        
        // 设备信息上报 0x0B
        FW_INFO_RESP_CMD = 0x0B,    // 设备信息上报
        
        // 授权指令 0x0C
        FW_AUTH_CMD = 0x0C,         // 授权指令，用于握手连接
        
        // 日志指令 0x0D
        FW_LOG_CMD = 0x0D,          // 日志指令，用于上传日志信息
        
        // 错误处理命令 0x0E-0x10
        ERRRESET_CMD = 0x0E,        // 错误复位
        ERRRECORDCLEAR_CMD = 0x0F,  // 错误记录清除
        GETERRRECORD_CMD = 0x10,    // 获取错误记录
        
        // 设备控制命令 0x11-0x12
        DEV_CTRL_CMD = 0x11,        // 采集设备控制命令
        SOFT_RST_CMD = 0x12,        // 采集设备软复位
        
        // 特殊升级命令 0xAD
        SPECIAL_UPDATE_RSP = 0xAD   // 透传升级命令(预留)
    }
}