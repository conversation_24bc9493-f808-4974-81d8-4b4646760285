using System.IO.Ports;
using CommunityToolkit.Mvvm.Messaging;
using NLog;
using TouchSocket.Core;
using TouchSocket.SerialPorts;
using TouchSocket.Sockets;
using GaugeCtrl.Communication.Adapters;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Messages;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.Packages;
using GaugeCtrl.Communication.RequestInfos;
using SerialPortConfig = GaugeCtrl.Communication.Models.SerialPortConfig;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 串口通讯服务
    /// 使用TouchSocket 3.1数据适配器进行数据解析，支持多种数据包类型
    /// </summary>
    public class SerialPortService : IDisposable
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private SerialPortClient? _serialClient;
        private bool _disposed = false;

        /// <summary>
        /// 示波器数据接收事件
        /// </summary>
        public event Action<OscilloscopeDataReport>? OscilloscopeDataReceived;

        /// <summary>
        /// 信号提取参数数据接收事件（来自USB2.0）
        /// </summary>
        public event Action<SignalExtractionParams>? SignalExtractionParamsReceived;
        
        
        public event Action<RespTypeParameter>? RespTypeParameterReceived;
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event Action<bool>? ConnectionStatusChanged;

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        public string[] GetAvailablePorts()
        {
            try
            {
                return SerialPort.GetPortNames();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "获取可用串口列表时发生错误");
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 连接串口
        /// </summary>
        /// <param name="config">串口配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectAsync(SerialPortConfig config)
        {
            try
            {
                if (_serialClient?.Online == true)
                {
                    await DisconnectAsync();
                }

                _serialClient = new SerialPortClient();
                _serialClient.Received = OnDataReceived;
                _serialClient.Closed = OnDisconnected;

                var touchConfig = new TouchSocketConfig()
                    .SetSerialPortOption(new SerialPortOption()
                    {
                        PortName = config.PortName,
                        BaudRate = config.BaudRate,
                        DataBits = config.DataBits,
                        Parity = config.Parity,
                        StopBits = config.StopBits
                    })
                    .SetSerialDataHandlingAdapter(() => new UnifiedProtocolDataAdapter());

                await _serialClient.SetupAsync(touchConfig);
                await _serialClient.ConnectAsync();

                config.IsConnected = _serialClient.Online;
                ConnectionStatusChanged?.Invoke(_serialClient.Online);

                Logger.Info($"串口连接成功: {config.PortName}, 波特率: {config.BaudRate}");

                // 连接成功后发送授权命令
                if (_serialClient.Online)
                {
                    await SendAuthCommandAsync();
                }

                return _serialClient.Online;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"连接串口失败: {config.PortName}");
                config.IsConnected = false;
                ConnectionStatusChanged?.Invoke(false);
                return false;
            }
        }

        /// <summary>
        /// 断开串口连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_serialClient?.Online == true)
                {
                    await _serialClient.CloseAsync();
                    Logger.Info("串口连接已断开");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "断开串口连接时发生错误");
            }
            finally
            {
                ConnectionStatusChanged?.Invoke(false);
            }
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendDataAsync(byte[] data)
        {
            try
            {
                if (_serialClient?.Online != true)
                {
                    Logger.Warn("串口未连接，无法发送数据");
                    return false;
                }

                await _serialClient.SendAsync(data);
                Logger.Debug($"发送数据成功，长度: {data.Length} 字节");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "发送数据时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 发送示波器参数设置命令
        /// </summary>
        /// <param name="parameter">示波器控制参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendOscilloscopeParameterSetCommandAsync(OscilloscopeControlParameter parameter,
            bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildOscilloscopeParameterSetCommand(parameter, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送授权命令
        /// </summary>
        public async Task<bool> SendAuthCommandAsync()
        {
            return await SendAuthCommandTraditionalAsync();
        }

        /// <summary>
        /// 检查连接状态
        /// </summary>
        public bool IsConnected => _serialClient?.Online == true;

        /// <summary>
        /// 发送授权命令（传统方式）
        /// </summary>
        public async Task<bool> SendAuthCommandTraditionalAsync()
        {
            var command = CommandBuilder.BuildAuthCommand();
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送设备控制命令
        /// </summary>
        /// <param name="command">控制命令</param>
        public async Task<bool> SendDeviceControlCommandAsync(DeviceControlCommand command)
        {
            try
            {
                if (_serialClient?.Online != true)
                {
                    Logger.Warn("串口未连接，无法发送设备控制命令");
                    return false;
                }

                // 使用新的RequestInfoBuilder方式发送
                var commandRequest = CommandRequestInfo.CreateDeviceControlCommand(command);
                await _serialClient.SendAsync(commandRequest);
                Logger.Debug($"设备控制命令发送成功: {command}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"发送设备控制命令时发生错误: {command}");
                return false;
            }
        }

        /// <summary>
        /// 发送设备控制命令（传统方式）
        /// </summary>
        /// <param name="command">控制命令</param>
        public async Task<bool> SendDeviceControlCommandTraditionalAsync(DeviceControlCommand command)
        {
            var commandFrame = CommandBuilder.BuildDeviceControlCommand(command);
            return await SendDataAsync(commandFrame);
        }

        /// <summary>
        /// 发送软复位命令
        /// </summary>
        public async Task<bool> SendSoftResetCommandAsync()
        {
            var command = CommandBuilder.BuildSoftResetCommand();
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送错误复位命令
        /// </summary>
        public async Task<bool> SendErrorResetCommandAsync()
        {
            var command = CommandBuilder.BuildErrorResetCommand();
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送错误记录清除命令
        /// </summary>
        public async Task<bool> SendErrorRecordClearCommandAsync()
        {
            var command = CommandBuilder.BuildErrorRecordClearCommand();
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送获取错误记录命令
        /// </summary>
        public async Task<bool> SendGetErrorRecordCommandAsync()
        {
            var command = CommandBuilder.BuildGetErrorRecordCommand();
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送固件版本请求命令
        /// </summary>
        public async Task<bool> SendFirmwareVersionRequestAsync()
        {
            var command = CommandBuilder.BuildFirmwareVersionRequestCommand();
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送系统参数设置命令
        /// </summary>
        /// <param name="parameter">系统参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendSystemParameterSetCommandAsync(SystemParameter parameter,
            bool isSingleDevice = true)
        {
            try
            {
                if (_serialClient?.Online != true)
                {
                    Logger.Warn("串口未连接，无法发送系统参数设置命令");
                    return false;
                }

                // 使用新的RequestInfoBuilder方式发送
                var commandRequest = CommandRequestInfo.CreateSystemParameterSetCommand(parameter, isSingleDevice);
                await _serialClient.SendAsync(commandRequest);
                Logger.Debug($"系统参数设置命令发送成功 - 通道: {parameter.ChannelSelection}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "发送系统参数设置命令时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 发送系统参数设置命令（传统方式）
        /// </summary>
        /// <param name="parameter">系统参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendSystemParameterSetCommandTraditionalAsync(SystemParameter parameter,
            bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildSystemParameterSetCommand(parameter, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送滤波参数设置命令
        /// </summary>
        /// <param name="parameter">滤波参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendFilterParameterSetCommandAsync(FilterParameter parameter,
            bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildFilterParameterSetCommand(parameter, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送时延参数设置命令
        /// </summary>
        /// <param name="parameter">时延参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendDelayParameterSetCommandAsync(DelayParameter parameter, bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildDelayParameterSetCommand(parameter, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送数据回传类型参数设置命令
        /// </summary>
        /// <param name="parameter">数据类型参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendDataTypeParameterSetCommandAsync(RespTypeParameter parameter,
            bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildDataTypeParameterSetCommand(parameter, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送触发阈值参数设置命令
        /// </summary>
        /// <param name="parameter">阈值参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendThresholdParameterSetCommandAsync(ThresholdParameter parameter,
            bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildThresholdParameterSetCommand(parameter, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送信号提取参数设置命令
        /// </summary>
        /// <param name="parameter">信号提取参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendSignalExtractionParameterSetCommandAsync(bool isUpload,
            bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildSignalExtractionParameterSetCommand(isUpload, isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送参数恢复出厂设置命令
        /// </summary>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendFactoryResetCommandAsync(bool isSingleDevice = true)
        {
            var command = CommandBuilder.BuildFactoryResetCommand(isSingleDevice);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送设备信息请求命令
        /// </summary>
        /// <param name="deviceNumber">设备编号</param>
        /// <param name="parameterType">参数类型</param>
        public async Task<bool> SendDeviceInfoRequestAsync(byte deviceNumber, ParameterType parameterType)
        {
            try
            {
                if (_serialClient?.Online != true)
                {
                    Logger.Warn("串口未连接，无法发送设备信息请求");
                    return false;
                }

                // 使用新的RequestInfoBuilder方式发送
                var commandRequest = CommandRequestInfo.CreateDeviceInfoRequestCommand(deviceNumber, parameterType);
                await _serialClient.SendAsync(commandRequest);
                Logger.Debug($"设备信息请求发送成功 - 设备: 0x{deviceNumber:X2}, 类型: {parameterType}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "发送设备信息请求时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 发送设备信息请求命令（传统方式）
        /// </summary>
        /// <param name="deviceNumber">设备编号</param>
        /// <param name="parameterType">参数类型</param>
        public async Task<bool> SendDeviceInfoRequestTraditionalAsync(byte deviceNumber, ParameterType parameterType)
        {
            var command = CommandBuilder.BuildDeviceInfoRequestCommand(deviceNumber, parameterType);
            return await SendDataAsync(command);
        }

        /// <summary>
        /// 发送自定义数据包命令
        /// </summary>
        /// <param name="data">要发送的数据包</param>
        public async Task<bool> SendCustomPackageAsync(byte[] data)
        {
            try
            {
                if (_serialClient?.Online != true)
                {
                    Logger.Warn("串口未连接，无法发送自定义数据包");
                    return false;
                }

                await _serialClient.SendAsync(data);
                Logger.Debug($"自定义数据包发送成功，长度: {data.Length} 字节");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "发送自定义数据包时发生错误");
                return false;
            }
        }


        /// <summary>
        /// 数据接收处理
        /// 使用统一的TouchSocket数据适配器自动解析协议数据
        /// </summary>
        private async Task OnDataReceived(ISerialPortClient client, ReceivedDataEventArgs e)
        {
            try
            {
                // 处理统一协议请求信息
                if (e.RequestInfo is UnifiedProtocolRequestInfo unifiedRequest)
                {
                    using var byteBlock = new ByteBlock(unifiedRequest.Frame?.Data);
                    // 根据数据包类型进行特殊处理
                    switch (unifiedRequest.PacketType)
                    {
                        case PacketType.OscilloscopeData:
                            var oscilloscopeDataPackage = new OscilloscopeDataPackage();
                            oscilloscopeDataPackage.Unpackage(byteBlock);
                            var oscilloscopeDataReport = new OscilloscopeDataReport
                            {
                                DataType = (OscilloscopeDataType)oscilloscopeDataPackage.DataType,
                                Channels = oscilloscopeDataPackage.Channels
                            };
                            OscilloscopeDataReceived?.Invoke(oscilloscopeDataReport);
                            break;
                        case PacketType.AuthCommand:
                            Logger.Info("收到授权应答");
                            break;
                        case PacketType.LogCommand:
                            HandleLogCommand(unifiedRequest);
                            break;
                        case PacketType.SystemParameter:
                            var systemParameterPackage = new SystemParameterPackage();
                            systemParameterPackage.Unpackage(byteBlock);
                            // 处理系统参数数据
                            var systemParameter = new SystemParameter
                            {
                                ChannelSelection = systemParameterPackage.ChannelSelection,
                                BaselineAdjustment = systemParameterPackage.BaselineAdjustment,
                                GainAdjustment = systemParameterPackage.GainAdjustment
                            };
                            // 发送系统参数上报事件
                            WeakReferenceMessenger.Default.Send(
                                new SystemParameterReceivedMessage(systemParameter));

                            break;

                        case PacketType.FilterParameter:
                            var filterParameterPackage = new FilterParameterPackage();
                            filterParameterPackage.Unpackage(byteBlock);
                            // 处理滤波参数数据
                            var filterParameter = new FilterParameter
                            {
                                AlgorithmType = filterParameterPackage.AlgorithmType,
                                LowPassCutoffFrequency = filterParameterPackage.LowPassCutoffFrequency,
                                LowPassSamplingPeriod = filterParameterPackage.LowPassSamplingPeriod,
                                HighPassCutoffFrequency = filterParameterPackage.HighPassCutoffFrequency,
                                HighPassSamplingPeriod = filterParameterPackage.HighPassSamplingPeriod,
                                WindowWidth = filterParameterPackage.WindowWidth
                            };
                            // 发送滤波参数上报事件
                            WeakReferenceMessenger.Default.Send(
                                new FilterParameterReceivedMessage(filterParameter));

                            break;

                        case PacketType.DelayParameter:
                            var delayParameterPackage = new DelayParameterPackage();
                            delayParameterPackage.Unpackage(byteBlock);
                            // 处理信号时延参数数据
                            var delayParameter = new DelayParameter
                            {
                                DelayTime = delayParameterPackage.DelayTime
                            };
                            // 发送信号时延参数上报事件
                            WeakReferenceMessenger.Default.Send(
                                new DelayParameterReceivedMessage(delayParameter));

                            break;

                        case PacketType.RespTypeParameter:
                            var respTypeParameter = new RespTypeParameterPackage();
                            respTypeParameter.Unpackage(byteBlock);
                            // 处理数据回传类型参数数据
                            var dataTypeParameter = new RespTypeParameter
                            {
                                UploadType = (DataUploadType)respTypeParameter.UploadType,
                                Channels = respTypeParameter.Channels
                            };
                            RespTypeParameterReceived?.Invoke(dataTypeParameter);

                            break;

                        case PacketType.ThresholdParameter:
                            var thresholdParameterPackage = new ThresholdParameterPackage();
                            thresholdParameterPackage.Unpackage(byteBlock);
                            // 处理触发阈值参数数据
                            var thresholdParameter = new ThresholdParameter
                            {
                                ThresholdMechanismOperation = thresholdParameterPackage.ThresholdMechanismOperation,
                                BitWidthExpandThreshold = thresholdParameterPackage.BitWidthExpandThreshold
                            };
                            // 发送触发阈值参数上报事件
                            WeakReferenceMessenger.Default.Send(
                                new ThresholdParameterReceivedMessage(thresholdParameter));
                            break;

                        case PacketType.SignalExtractionParameter:
                        {
                            // 发送信号提取参数上报事件
                            var package = new SignalExtractionParamsPackage();
                            package.Unpackage(byteBlock);

                            var signalExtractionParams = new SignalExtractionParams()
                            {
                                Channels = package.Channels
                            };
                            SignalExtractionParamsReceived?.Invoke(signalExtractionParams);

                            break;
                        }

                        case PacketType.ErrorRecordData:


                            break;
                        default:
                            Logger.Debug($"收到其他类型数据: {unifiedRequest.PacketType}");
                            break;
                    }
                }
                else
                {
                    // 如果不是预期的数据格式，记录原始数据
                    var data = e.ByteBlock.ToArray();
                    Logger.Debug($"接收到未解析数据，长度: {data.Length} 字节");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "处理接收数据时发生错误");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 处理日志命令
        /// </summary>
        private void HandleLogCommand(UnifiedProtocolRequestInfo protocolRequest)
        {
            try
            {
                var commandData = protocolRequest.GetCommandData();
                if (commandData.Length > 0)
                {
                    var logMessage = System.Text.Encoding.UTF8.GetString(commandData);
                    Logger.Info($"设备日志: {logMessage}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "处理日志命令时发生错误");
            }
        }

        /// <summary>
        /// 连接断开处理
        /// </summary>
        private async Task OnDisconnected(ISerialPortClient client, ClosedEventArgs e)
        {
            Logger.Info($"串口连接断开: {e.Message}");
            ConnectionStatusChanged?.Invoke(false);
            await Task.CompletedTask;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    DisconnectAsync().Wait(1000);
                    _serialClient?.Dispose();
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "释放串口资源时发生错误");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }
    }
}