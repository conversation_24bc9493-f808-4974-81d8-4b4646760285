using System.IO.Ports;
using GaugeCtrl.Communication.Enums;
using TouchSocket.Core;

namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// 系统参数模型
    /// </summary>
    public class SystemParameter
    {
        public ushort ChannelSelection { get; set; }

        public ushort GainAdjustment { get; set; }

        public uint BaselineAdjustment { get; set; }
    }

    /// <summary>
    /// 滤波参数模型
    /// </summary>
    public class FilterParameter
    {
        /// <summary>
        /// 算法激活类型选择 (0x01-低通滤波，0x02-高通滤波，0x03-滑动平均)
        /// </summary>
        public FilterType AlgorithmType { get; set; }

        /// <summary>
        /// 截止频率(针对低通滤波) - 单位KHz
        /// </summary>
        public uint LowPassCutoffFrequency { get; set; }

        /// <summary>
        /// 采样周期(针对低通滤波) - 单位ms
        /// </summary>
        public uint LowPassSamplingPeriod { get; set; }

        /// <summary>
        /// 截止频率(针对高通滤波) - 单位KHz
        /// </summary>
        public uint HighPassCutoffFrequency { get; set; }

        /// <summary>
        /// 采样周期(针对高通滤波) - 单位ms
        /// </summary>
        public uint HighPassSamplingPeriod { get; set; }

        /// <summary>
        /// 窗口宽度(针对滑动平均)
        /// </summary>
        public uint WindowWidth { get; set; }
    }

    /// <summary>
    /// 信号时延参数模型
    /// </summary>
    public class DelayParameter
    {
        /// <summary>
        /// 信号延迟时长△T (单位ms)
        /// </summary>
        public uint DelayTime { get; set; }
    }

    /// <summary>
    /// 数据回传类型参数模型
    /// </summary>
    public class RespTypeParameter
    {
        /// <summary>
        /// 数据上传类型
        /// </summary>
        public DataUploadType UploadType { get; set; }

        /// <summary>
        /// 8通道数据
        /// </summary>
        public uint[] Channels { get; set; } = [8];
    }

    /// <summary>
    /// 触发阈值参数模型
    /// </summary>
    public class ThresholdParameter
    {
        /// <summary>
        /// 触发阈值机制操作 (0x01-启动，0x02-不启动)
        /// </summary>
        public ushort ThresholdMechanismOperation { get; set; }

        /// <summary>
        /// 位宽扩展触发阈值 - 单位mv
        /// </summary>
        public uint BitWidthExpandThreshold { get; set; }
    }

    /// <summary>
    /// 信号提取参数模型
    /// </summary>
    public record SignalExtractionParameter : IPackage
    {
        /// <summary>
        /// 信号高度H
        /// </summary>
        public uint SignalHeight { get; set; }

        /// <summary>
        /// 信号宽度W
        /// </summary>
        public uint SignalWidth { get; set; }

        /// <summary>
        /// 信号面积A
        /// </summary>
        public uint SignalArea { get; set; }

        public void Package<TByteBlock>(ref TByteBlock byteBlock) where TByteBlock : IByteBlock
        {
            SignalHeight = byteBlock.ReadVarUInt32();
            SignalWidth = byteBlock.ReadVarUInt32();
            SignalArea = byteBlock.ReadVarUInt32();
        }

        public void Unpackage<TByteBlock>(ref TByteBlock byteBlock) where TByteBlock : IByteBlock
        {
            byteBlock.WriteVarUInt32(SignalHeight);
            byteBlock.WriteVarUInt32(SignalWidth);
            byteBlock.WriteVarUInt32(SignalArea);
        }
    }

    public record SignalExtractionParams
    {
        public Dictionary<int, SignalExtractionParameter> Channels { get; set; } = new();
    }

    /// <summary>
    /// 错误记录数据模型
    /// </summary>
    public class ErrorRecordData
    {
        /// <summary>
        /// 发生错误的通道 (0-7代表1-8通道)
        /// </summary>
        public byte ErrorChannel { get; set; }

        /// <summary>
        /// 故障数据类型 (0x01-原始数据，0x02-基线去除后的数据，0x03-位宽扩展后的数据，0x04-参数提取的H数据，0x05-参数提取的W数据，0x06-参数提取的A数据)
        /// </summary>
        public byte ErrorDataType { get; set; }

        /// <summary>
        /// 最终索引
        /// </summary>
        public short FinalIndex { get; set; }

        /// <summary>
        /// 错误数据 (1000个int32类型)
        /// </summary>
        public int[] ErrorData { get; set; } = new int[1000];

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}