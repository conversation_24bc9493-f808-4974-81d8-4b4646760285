using System.IO.Ports;
using CommunityToolkit.Mvvm.ComponentModel;

namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// 串口配置信息
    /// </summary>
    public partial class SerialPortConfig : ObservableObject
    {
        /// <summary>
        /// 串口名称
        /// </summary>
        [ObservableProperty]
        private string _portName = string.Empty;

        /// <summary>
        /// 波特率
        /// </summary>
        [ObservableProperty]
        private int _baudRate = 115200;

        /// <summary>
        /// 数据位
        /// </summary>
        [ObservableProperty]
        private int _dataBits = 8;

        /// <summary>
        /// 奇偶校验
        /// </summary>
        [ObservableProperty]
        private Parity _parity = Parity.None;

        /// <summary>
        /// 停止位
        /// </summary>
        [ObservableProperty]
        private StopBits _stopBits = StopBits.One;

        /// <summary>
        /// 连接状态
        /// </summary>
        [ObservableProperty]
        private bool _isConnected = false;

        /// <summary>
        /// 串口类型（用于区分USB2.0和USB3.0）
        /// </summary>
        [ObservableProperty]
        private SerialPortType _portType = SerialPortType.Usb2;

        /// <summary>
        /// 串口描述
        /// </summary>
        [ObservableProperty]
        private string _description = string.Empty;

        /// <summary>
        /// 创建默认的USB2.0串口配置
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <returns>USB2.0串口配置</returns>
        public static SerialPortConfig CreateUsb2Config(string portName)
        {
            return new SerialPortConfig
            {
                PortName = portName,
                BaudRate = 115200,
                DataBits = 8,
                Parity = Parity.None,
                StopBits = StopBits.One,
                PortType = SerialPortType.Usb2,
                Description = "USB2.0总线 - 参数配置和控制"
            };
        }

        /// <summary>
        /// 创建默认的USB3.0串口配置
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <returns>USB3.0串口配置</returns>
        public static SerialPortConfig CreateUsb3Config(string portName)
        {
            return new SerialPortConfig
            {
                PortName = portName,
                BaudRate = 921600,
                DataBits = 8,
                Parity = Parity.None,
                StopBits = StopBits.One,
                PortType = SerialPortType.Usb3,
                Description = "USB3.0总线 - 数据传输"
            };
        }

        /// <summary>
        /// 复制配置
        /// </summary>
        /// <returns>配置副本</returns>
        public SerialPortConfig Clone()
        {
            return new SerialPortConfig
            {
                PortName = PortName,
                BaudRate = BaudRate,
                DataBits = DataBits,
                Parity = Parity,
                StopBits = StopBits,
                IsConnected = IsConnected,
                PortType = PortType,
                Description = Description
            };
        }

        /// <summary>
        /// 获取配置字符串表示
        /// </summary>
        /// <returns>配置字符串</returns>
        public override string ToString()
        {
            return $"{PortName} ({BaudRate}, {DataBits}, {Parity}, {StopBits}) - {Description}";
        }
    }

    /// <summary>
    /// 串口类型枚举
    /// </summary>
    public enum SerialPortType
    {
        /// <summary>
        /// USB2.0总线 - 用于参数配置和控制
        /// </summary>
        Usb2 = 0,

        /// <summary>
        /// USB3.0总线 - 用于数据传输
        /// </summary>
        Usb3 = 1
    }
}