using GaugeCtrl.Communication.Constants;

namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// 协议帧基础模型
    /// </summary>
    public class ProtocolFrame
    {
        public byte Head1 { get; set; } = ProtocolConstants.FRAME_HEAD1;
        public byte Head2 { get; set; } = ProtocolConstants.FRAME_HEAD2;
        public byte DstAddr { get; set; }
        public byte SrcAddr { get; set; }
        public ushort DataSize { get; set; }
        public byte[] Data { get; set; } = Array.Empty<byte>();
        public ushort Crc { get; set; }
        public byte Tail1 { get; set; } = ProtocolConstants.FRAME_TAIL1;
        public byte Tail2 { get; set; } = ProtocolConstants.FRAME_TAIL2;

        /// <summary>
        /// 获取完整帧的字节数组
        /// </summary>
        public byte[] ToByteArray()
        {
            var frameSize = 10 + Data.Length; // 固定头部(6) + 数据 + CRC(2) + 尾部(2)
            var frame = new byte[frameSize];
            var index = 0;

            frame[index++] = Head1;
            frame[index++] = Head2;
            frame[index++] = DstAddr;
            frame[index++] = SrcAddr;
            
            // 数据长度 (小端序)
            var dataSizeBytes = BitConverter.GetBytes(DataSize);
            frame[index++] = dataSizeBytes[0];
            frame[index++] = dataSizeBytes[1];
            
            // 数据
            Array.Copy(Data, 0, frame, index, Data.Length);
            index += Data.Length;
            
            // CRC (小端序)
            var crcBytes = BitConverter.GetBytes(Crc);
            frame[index++] = crcBytes[0];
            frame[index++] = crcBytes[1];
            
            frame[index++] = Tail1;
            frame[index] = Tail2;

            return frame;
        }
    }
}