using GaugeCtrl.Communication.Constants;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Models;
using TouchSocket.Core;
using NLog;

namespace GaugeCtrl.Communication.RequestInfos
{
    /// <summary>
    /// 命令请求信息
    /// 专门用于构建和发送命令
    /// </summary>
    public class CommandRequestInfo : IRequestInfoBuilder
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 命令码
        /// </summary>
        public CommandCode CommandCode { get; set; }

        /// <summary>
        /// 命令数据
        /// </summary>
        public byte[] CommandData { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// 源地址
        /// </summary>
        public byte SourceAddress { get; set; } = ProtocolConstants.MASTER_ADDR;

        /// <summary>
        /// 目标地址
        /// </summary>
        public byte DestinationAddress { get; set; } = ProtocolConstants.LOCAL_ADDR;

        public void Build<TByteBlock>(ref TByteBlock byteBlock) where TByteBlock : IByteBlock
        {
            try
            {
                // 构建完整的数据包
                var data = new List<byte> { (byte)CommandCode };
                if (CommandData.Length > 0)
                {
                    data.AddRange(CommandData);
                }

                // 使用协议帧解析器构建完整帧
                var frameBytes = ProtocolFrameParser.BuildFrame(SourceAddress, DestinationAddress, data.ToArray());
                byteBlock.Write(frameBytes);

                Logger.Debug($"构建命令请求 - 命令码: 0x{(byte)CommandCode:X2}, 数据长度: {CommandData.Length}");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "构建命令请求时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 最大长度
        /// </summary>
        public int MaxLength => 1024;

        /// <summary>
        /// 创建授权命令请求
        /// </summary>
        public static CommandRequestInfo CreateAuthCommand()
        {
            return new CommandRequestInfo
            {
                CommandCode = CommandCode.FW_AUTH_CMD
            };
        }

        /// <summary>
        /// 创建设备控制命令请求
        /// </summary>
        public static CommandRequestInfo CreateDeviceControlCommand(DeviceControlCommand command)
        {
            return new CommandRequestInfo
            {
                CommandCode = CommandCode.DEV_CTRL_CMD,
                CommandData = new byte[] { (byte)command }
            };
        }

        /// <summary>
        /// 创建系统参数设置命令请求
        /// </summary>
        public static CommandRequestInfo CreateSystemParameterSetCommand(SystemParameter parameter, bool isSingleDevice = true)
        {
            var commandData = new List<byte>
            {
                (byte)ParameterType.SystemParameter,
                (byte)(isSingleDevice ? 0x01 : 0x02)
            };

            commandData.AddRange(BitConverter.GetBytes(parameter.ChannelSelection));
            commandData.AddRange(BitConverter.GetBytes(parameter.GainAdjustment));
            commandData.AddRange(BitConverter.GetBytes(parameter.BaselineAdjustment));

            return new CommandRequestInfo
            {
                CommandCode = CommandCode.FW_HANDLE_PARA_INFO_SET,
                CommandData = commandData.ToArray()
            };
        }

        /// <summary>
        /// 创建设备信息请求命令
        /// </summary>
        public static CommandRequestInfo CreateDeviceInfoRequestCommand(byte deviceNumber, ParameterType parameterType)
        {
            return new CommandRequestInfo
            {
                CommandCode = CommandCode.FW_INFO_RESP_CMD,
                CommandData = [deviceNumber, (byte)parameterType]
            };
        }
    }
}