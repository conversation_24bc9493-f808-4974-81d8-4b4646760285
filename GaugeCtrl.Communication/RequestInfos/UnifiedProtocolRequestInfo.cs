using GaugeCtrl.Communication.Constants;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Models;
using TouchSocket.Core;
using NLog;

namespace GaugeCtrl.Communication.RequestInfos
{
    /// <summary>
    /// 统一协议请求信息
    /// 处理所有类型的协议数据包解析
    /// </summary>
    public class  UnifiedProtocolRequestInfo : IFixedHeaderRequestInfo
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 数据体长度
        /// </summary>
        public int BodyLength { get; private set; }

        /// <summary>
        /// 解析后的协议帧
        /// </summary>
        public ProtocolFrame? Frame { get; private set; }

        /// <summary>
        /// 原始数据
        /// </summary>
        public byte[] RawData { get; private set; } = [];
        
        /// <summary>
        /// 数据包类型
        /// </summary>
        public PacketType PacketType { get; private set; } = PacketType.Unknown;

        /// <summary>
        /// 解析包头
        /// </summary>
        /// <param name="header">包头数据 (6字节: 帧头1 + 帧头2 + 目标地址 + 源地址 + 数据长度)</param>
        /// <returns>解析是否成功</returns>
        public bool OnParsingHeader(ReadOnlySpan<byte> header)
        {
            try
            {
                if (header.Length != 6)
                {
                    Logger.Warn($"协议帧头长度错误: {header.Length}");
                    return false;
                }

                // 验证帧头
                if (header[0] != ProtocolConstants.FRAME_HEAD1 || header[1] != ProtocolConstants.FRAME_HEAD2)
                {
                    Logger.Warn($"协议帧头验证失败: 0x{header[0]:X2} 0x{header[1]:X2}");
                    return false;
                }

                // 解析数据长度 (小端序)
                var dataSize = BitConverter.ToUInt16(header.Slice(4, 2));
                
                // 设置后续数据长度 (数据 + CRC(2字节) + 帧尾(2字节))
                this.BodyLength = dataSize + 4;

                Logger.Debug($"协议帧头解析成功，数据长度: {dataSize}, 总体长度: {this.BodyLength}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "解析协议帧头时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 解析包体
        /// </summary>
        /// <param name="body">包体数据 (数据 + CRC + 帧尾)</param>
        /// <returns>解析是否成功</returns>
        public bool OnParsingBody(ReadOnlySpan<byte> body)
        {
            try
            {
                if (body.Length != this.BodyLength)
                {
                    Logger.Warn($"协议帧体长度不匹配，期望: {this.BodyLength}, 实际: {body.Length}");
                    return false;
                }

                // 验证帧尾
                var tailIndex = body.Length - 2;
                if (body[tailIndex] != ProtocolConstants.FRAME_TAIL1 || body[tailIndex + 1] != ProtocolConstants.FRAME_TAIL2)
                {
                    Logger.Warn($"协议帧尾验证失败: 0x{body[tailIndex]:X2} 0x{body[tailIndex + 1]:X2}");
                    return false;
                }

                // 构建完整帧数据进行解析
                var frameLength = 6 + body.Length; // 头部6字节 + 体部
                var frameData = new byte[frameLength];
                
                // 重新构建帧头
                frameData[0] = ProtocolConstants.FRAME_HEAD1;
                frameData[1] = ProtocolConstants.FRAME_HEAD2;
                frameData[2] = ProtocolConstants.LOCAL_ADDR;  // 假设目标地址
                frameData[3] = ProtocolConstants.MASTER_ADDR; // 假设源地址
                
                var dataSize = (ushort)(body.Length - 4); // 减去CRC和帧尾
                var dataSizeBytes = BitConverter.GetBytes(dataSize);
                frameData[4] = dataSizeBytes[0];
                frameData[5] = dataSizeBytes[1];
                
                // 复制体部数据
                body.CopyTo(frameData.AsSpan(6));

                // 使用协议帧解析器解析完整帧
                this.Frame = ProtocolFrameParser.ParseFrame(frameData);
                this.RawData = frameData;

                if (this.Frame != null)
                {
                    // 根据命令码和数据内容判断数据包类型并解析
                    ParseSpecificData();
                    
                    Logger.Debug($"协议帧解析成功 - 命令码: 0x{(this.Frame.Data.Length > 0 ? this.Frame.Data[0] : 0):X2}, 类型: {PacketType}");
                    return true;
                }

                Logger.Warn("协议帧解析失败");
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "解析协议帧体时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 解析特定类型的数据
        /// </summary>
        private void ParseSpecificData()
        {
            if (Frame?.Data == null || Frame.Data.Length == 0)
            {
                PacketType = PacketType.Unknown;
                return;
            }

            var commandCode = (CommandCode)Frame.Data[0];
            var commandData = GetCommandData();

            switch (commandCode)
            {
                case CommandCode.FW_INFO_RESP_CMD:
                    ParseInfoResponseData(commandData);
                    break;
                case CommandCode.FW_AUTH_CMD:
                    PacketType = PacketType.AuthCommand;
                    break;
                case CommandCode.FW_LOG_CMD:
                    PacketType = PacketType.LogCommand;
                    break;
                case CommandCode.DEV_CTRL_CMD:
                    PacketType = PacketType.DeviceControlCommand;
                    break;
                default:
                    PacketType = PacketType.GenericCommand;
                    break;
            }
        }

        /// <summary>
        /// 解析信息响应数据
        /// </summary>
        private void ParseInfoResponseData(byte[] commandData)
        {
            if (commandData.Length < 2) return;

            var parameterType = (ParameterType)commandData[1];

            PacketType = parameterType switch
            {
                ParameterType.OscilloscopeData => PacketType.OscilloscopeData,
                ParameterType.SystemParameter => PacketType.SystemParameter,
                ParameterType.FilterParameter => PacketType.FilterParameter,
                ParameterType.DelayParameter => PacketType.DelayParameter,
                ParameterType.RespTypeParameter => PacketType.RespTypeParameter,
                ParameterType.ThresholdParameter => PacketType.ThresholdParameter,
                ParameterType.ExtractionParameter => PacketType.SignalExtractionParameter,
                ParameterType.ErrorData => PacketType.ErrorRecordData,
                _ => PacketType.UnknownParameter
            };
        }

        /// <summary>
        /// 获取命令码
        /// </summary>
        /// <returns>命令码，如果无数据返回null</returns>
        public CommandCode? GetCommandCode()
        {
            if (Frame?.Data != null && Frame.Data.Length > 0)
            {
                return (CommandCode)Frame.Data[0];
            }
            return null;
        }

        /// <summary>
        /// 获取命令数据
        /// </summary>
        /// <returns>命令数据，不包含命令码</returns>
        public byte[] GetCommandData()
        {
            if (Frame?.Data != null && Frame.Data.Length > 1)
            {
                var commandData = new byte[Frame.Data.Length - 1];
                Array.Copy(Frame.Data, 1, commandData, 0, commandData.Length);
                return commandData;
            }
            return Array.Empty<byte>();
        }
    }

    /// <summary>
    /// 数据包类型枚举
    /// </summary>
    public enum PacketType
    {
        Unknown,
        AuthCommand,
        LogCommand,
        DeviceControlCommand,
        GenericCommand,
        OscilloscopeData,
        SystemParameter,
        FilterParameter,
        DelayParameter,
        RespTypeParameter,
        ThresholdParameter,
        SignalExtractionParameter,
        ErrorRecordData,
        UnknownParameter
    }

}