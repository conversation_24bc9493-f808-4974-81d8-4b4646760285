using GaugeCtrl.Communication.Constants;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.Utils;
using NLog;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 协议帧解析器
    /// 负责解析和构建协议帧
    /// </summary>
    public static class ProtocolFrameParser
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 解析协议帧
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>解析后的协议帧，解析失败返回null</returns>
        public static ProtocolFrame? ParseFrame(byte[] data)
        {
            try
            {
                if (data.Length < 10) // 最小帧长度
                {
                    Logger.Warn($"数据长度不足，无法解析协议帧: {data.Length} 字节");
                    return null;
                }

                var frame = new ProtocolFrame();
                var index = 0;

                // 验证帧头
                frame.Head1 = data[index++];
                frame.Head2 = data[index++];
                if (frame.Head1 != ProtocolConstants.FRAME_HEAD1 || frame.Head2 != ProtocolConstants.FRAME_HEAD2)
                {
                    Logger.Warn($"帧头验证失败: 0x{frame.Head1:X2} 0x{frame.Head2:X2}");
                    return null;
                }

                // 解析地址
                frame.DstAddr = data[index++];
                frame.SrcAddr = data[index++];

                // 解析数据长度 (小端序)
                frame.DataSize = BitConverter.ToUInt16(data, index);
                index += 2;

                // 验证数据长度
                if (data.Length < 10 + frame.DataSize)
                {
                    Logger.Warn($"数据长度不匹配，期望: {10 + frame.DataSize}, 实际: {data.Length}");
                    return null;
                }

                // 解析数据
                frame.Data = new byte[frame.DataSize];
                Array.Copy(data, index, frame.Data, 0, frame.DataSize);
                index += frame.DataSize;

                // 解析CRC (小端序)
                frame.Crc = BitConverter.ToUInt16(data, index);
                index += 2;

                // 验证帧尾
                frame.Tail1 = data[index++];
                frame.Tail2 = data[index];
                if (frame.Tail1 != ProtocolConstants.FRAME_TAIL1 || frame.Tail2 != ProtocolConstants.FRAME_TAIL2)
                {
                    Logger.Warn($"帧尾验证失败: 0x{frame.Tail1:X2} 0x{frame.Tail2:X2}");
                    return null;
                }

                // 验证CRC
                // var crcData = new byte[6 + frame.DataSize]; // 头部 + 数据
                // Array.Copy(data, 0, crcData, 0, crcData.Length);
                var calculatedCrc = CrcCalculator.CalculateCrc16(frame.Data);
                
                if (calculatedCrc != frame.Crc)
                {
                    Logger.Warn($"CRC校验失败，计算值: 0x{calculatedCrc:X4}, 接收值: 0x{frame.Crc:X4}");
                    return null;
                }

                Logger.Debug($"协议帧解析成功 - 源地址: 0x{frame.SrcAddr:X2}, 目标地址: 0x{frame.DstAddr:X2}, 数据长度: {frame.DataSize}");
                return frame;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "解析协议帧时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 构建协议帧
        /// </summary>
        /// <param name="srcAddr">源地址</param>
        /// <param name="dstAddr">目标地址</param>
        /// <param name="data">数据</param>
        /// <returns>完整的协议帧字节数组</returns>
        public static byte[] BuildFrame(byte srcAddr, byte dstAddr, byte[] data)
        {
            try
            {
                var frame = new ProtocolFrame
                {
                    SrcAddr = srcAddr,
                    DstAddr = dstAddr,
                    DataSize = (ushort)data.Length,
                    Data = data
                };

                // 计算CRC (头部 + 数据)
                // var crcData = new byte[6 + data.Length];
                // var index = 0;
                //
                // crcData[index++] = frame.Head1;
                // crcData[index++] = frame.Head2;
                // crcData[index++] = frame.DstAddr;
                // crcData[index++] = frame.SrcAddr;
                //
                // var dataSizeBytes = BitConverter.GetBytes(frame.DataSize);
                // crcData[index++] = dataSizeBytes[0];
                // crcData[index++] = dataSizeBytes[1];
                //
                // Array.Copy(data, 0, crcData, index, data.Length);
                
                frame.Crc = CrcCalculator.CalculateCrc16(data);

                Logger.Debug($"构建协议帧 - 源地址: 0x{srcAddr:X2}, 目标地址: 0x{dstAddr:X2}, 数据长度: {data.Length}");
                return frame.ToByteArray();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "构建协议帧时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 构建命令帧（无数据）
        /// </summary>
        /// <param name="commandCode">命令码</param>
        /// <returns>完整的协议帧字节数组</returns>
        public static byte[] BuildCommandFrame(CommandCode commandCode)
        {
            var data = new byte[] { (byte)commandCode };
            return BuildFrame(ProtocolConstants.MASTER_ADDR, ProtocolConstants.LOCAL_ADDR, data);
        }

        /// <summary>
        /// 构建命令帧（带数据）
        /// </summary>
        /// <param name="commandCode">命令码</param>
        /// <param name="commandData">命令数据</param>
        /// <returns>完整的协议帧字节数组</returns>
        public static byte[] BuildCommandFrame(CommandCode commandCode, byte[] commandData)
        {
            var data = new List<byte> { (byte)commandCode };
            if (commandData.Length > 0)
            {
                data.AddRange(commandData);
            }

            return BuildFrame(ProtocolConstants.MASTER_ADDR, ProtocolConstants.LOCAL_ADDR, data.ToArray());
        }
    }
}