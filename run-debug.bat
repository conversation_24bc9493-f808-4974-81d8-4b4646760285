@echo off
chcp 65001 >nul
echo ========================================
echo GaugeCtrl Application - Debug Mode
echo ========================================
echo.

REM Check if .NET 8 SDK is installed
echo Checking .NET 8 SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET SDK not found, please install .NET 8 SDK first
    pause
    exit /b 1
)

echo .NET SDK Version:
dotnet --version
echo.

REM Restore NuGet packages
echo Restoring NuGet packages...
dotnet restore GaugeCtrl.sln
if %errorlevel% neq 0 (
    echo Error: NuGet package restore failed
    pause
    exit /b 1
)
echo.

REM Run in debug mode
echo Starting application in debug mode...
echo Note: Debug mode will show detailed console output
echo.

cd GaugeCtrl
dotnet run --configuration Debug

echo.
echo Application exited
pause