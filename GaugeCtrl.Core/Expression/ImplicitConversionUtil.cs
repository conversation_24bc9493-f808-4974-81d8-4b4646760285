namespace GaugeCtrl.Core.Expression;

/// <summary>
///     隐式转换帮助类
/// </summary>
public class ImplicitConversionUtil
{
    /// <summary>
    ///     隐式转换表
    /// </summary>
    private static readonly Dictionary<Type, HashSet<Type>> ImplicitConversionDictionary = new(10);

    static ImplicitConversionUtil()
    {
        ImplicitConversionDictionary[typeof(sbyte)] = new HashSet<Type>(new[]
        {
            typeof(short),
            typeof(int),
            typeof(long),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(byte)] = new HashSet<Type>(new[]
        {
            typeof(short),
            typeof(ushort),
            typeof(int),
            typeof(uint),
            typeof(long),
            typeof(ulong),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(short)] = new HashSet<Type>(new[]
        {
            typeof(int),
            typeof(long),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(ushort)] = new HashSet<Type>(new[]
        {
            typeof(int),
            typeof(uint),
            typeof(long),
            typeof(ulong),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(int)] = new HashSet<Type>(new[]
        {
            typeof(long),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(uint)] = new HashSet<Type>(new[]
        {
            typeof(long),
            typeof(ulong),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(long)] = new HashSet<Type>(new[]
        {
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(char)] = new HashSet<Type>(new[]
        {
            typeof(ushort),
            typeof(int),
            typeof(uint),
            typeof(long),
            typeof(ulong),
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
        ImplicitConversionDictionary[typeof(float)] = new HashSet<Type>(new[]
        {
            typeof(double)
        });
        ImplicitConversionDictionary[typeof(ulong)] = new HashSet<Type>(new[]
        {
            typeof(float),
            typeof(double),
            typeof(decimal)
        });
    }

    /// <summary>
    ///     判断是否可用进行隐式转换
    /// </summary>
    /// <param name="fromType">从什么类型</param>
    /// <param name="arriveType">到什么类型</param>
    /// <returns>是否可用进行隐式转换</returns>
    public static bool ImplicitConversion(Type fromType, Type arriveType)
    {
        HashSet<Type> set;
        return ImplicitConversionDictionary.TryGetValue(fromType, out set) && set.Contains(arriveType);
    }
}