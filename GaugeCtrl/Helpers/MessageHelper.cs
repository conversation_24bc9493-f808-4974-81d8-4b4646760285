using HandyControl.Controls;
using HandyControl.Data;
using NLog;
using System;
using System.Windows;

namespace GaugeCtrl.Helpers
{
    /// <summary>
    /// 消息帮助类，基于HandyControl的Growl组件，并集成日志记录功能
    /// </summary>
    public static class MessageHelper
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 显示信息消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题（可选）</param>
        public static void Info(string message, string title = "信息")
        {
            Logger.Info($"{title}: {message}");
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Info(new GrowlInfo
                {
                    Message = message,
                    WaitTime = 3,
                    ActionBeforeClose = isConfirmed => true
                });
            });
        }

        /// <summary>
        /// 显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题（可选）</param>
        public static void Success(string message, string title = "成功")
        {
            Logger.Info($"{title}: {message}");
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Success(new GrowlInfo
                {
                    Message = message,
                    WaitTime = 3,
                    ActionBeforeClose = isConfirmed => true
                });
            });
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题（可选）</param>
        public static void Warning(string message, string title = "警告")
        {
            Logger.Warn($"{title}: {message}");
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Warning(new GrowlInfo
                {
                    Message = message,
                    WaitTime = 4,
                    ActionBeforeClose = isConfirmed => true
                });
            });
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题（可选）</param>
        public static void Error(string message, string title = "错误")
        {
            Logger.Error($"{title}: {message}");
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Error(new GrowlInfo
                {
                    Message = message,
                    WaitTime = 5,
                    ActionBeforeClose = isConfirmed => true
                });
            });
        }

        /// <summary>
        /// 显示错误消息（带异常信息）
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="exception">异常对象</param>
        /// <param name="title">消息标题（可选）</param>
        public static void Error(string message, Exception exception, string title = "错误")
        {
            Logger.Error(exception, $"{title}: {message}");
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Error(new GrowlInfo
                {
                    Message = $"{message}: {exception.Message}",
                    WaitTime = 5,
                    ActionBeforeClose = isConfirmed => true
                });
            });
        }

        /// <summary>
        /// 显示询问消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题（可选）</param>
        /// <param name="onConfirm">确认回调</param>
        /// <param name="onCancel">取消回调</param>
        public static void Ask(string message, string title = "确认", Action? onConfirm = null, Action? onCancel = null)
        {
            Logger.Info($"{title}: {message}");
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Ask(new GrowlInfo
                {
                    Message = message,
                    CancelStr = "取消",
                    ConfirmStr = "确认",
                    ActionBeforeClose = isConfirmed =>
                    {
                        if (isConfirmed)
                        {
                            onConfirm?.Invoke();
                        }
                        else
                        {
                            onCancel?.Invoke();
                        }
                        return true;
                    }
                });
            });
        }

        /// <summary>
        /// 显示致命错误消息（用于应用程序级别的严重错误）
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="exception">异常对象</param>
        /// <param name="title">消息标题（可选）</param>
        public static void Fatal(string message, Exception? exception = null, string title = "致命错误")
        {
            if (exception != null)
            {
                Logger.Fatal(exception, $"{title}: {message}");
            }
            else
            {
                Logger.Fatal($"{title}: {message}");
            }
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                Growl.Fatal(new GrowlInfo
                {
                    Message = exception != null ? $"{message}: {exception.Message}" : message,
                    WaitTime = 0, // 不自动关闭
                    ActionBeforeClose = isConfirmed => true
                });
            });
        }
    }
}