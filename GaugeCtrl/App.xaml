<Application x:Class="GaugeCtrl.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:hc="https://handyorg.github.io/handycontrol"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="Boolean2VisibilityConverter"/>
            
            <!-- 自定义样式 -->
            <Style x:Key="PlotStyle" TargetType="Border">
                <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="4"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>