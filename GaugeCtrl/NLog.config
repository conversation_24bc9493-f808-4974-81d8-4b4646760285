<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="${basedir}/Logs/internal-nlog.txt">

  <!-- 定义变量 -->
  <variable name="logDirectory" value="${basedir}/Logs"/>
  
  <!-- 定义目标 -->
  <targets>
    <!-- 文件目标 -->
    <target xsi:type="File"
            name="fileTarget"
            fileName="${logDirectory}/OscilloscopeApp-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true:padding=-5} ${logger:shortName=true} - ${message} ${exception:format=tostring}"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="7"
            encoding="utf-8"/>
    
    <!-- 控制台目标 -->
    <target xsi:type="Console"
            name="consoleTarget"
            layout="${time} [${level:uppercase=true:padding=-5}] ${logger:shortName=true} - ${message} ${exception:format=tostring}" encoding="utf-8"/>
    
    <!-- 调试输出目标 -->
    <target xsi:type="Debugger"
            name="debugTarget"
            layout="${time} [${level:uppercase=true}] ${logger:shortName=true} - ${message}" encoding="utf-8"/>
  </targets>

  <!-- 定义规则 -->
  <rules>
    <!-- 所有日志写入文件 -->
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    
    <!-- Debug模式下输出到控制台 -->
    <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
    
    <!-- Debug模式下输出到调试器 -->
    <logger name="*" minlevel="Debug" writeTo="debugTarget" />
  </rules>
</nlog>