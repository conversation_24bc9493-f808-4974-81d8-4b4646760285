using System.Windows;
using NLog;
using NLog.Config;
using NLog.Targets;

namespace GaugeCtrl
{
    public partial class App : Application
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置NLog
            ConfigureLogging();
            
            // 设置全局异常处理
            SetupExceptionHandling();
            
            Logger.Info("应用程序启动");
            
            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Logger.Info("应用程序退出");
            LogManager.Shutdown();
            base.OnExit(e);
        }

        private void ConfigureLogging()
        {
            var config = new LoggingConfiguration();

            // 文件目标
            var fileTarget = new FileTarget("fileTarget")
            {
                FileName = "${basedir}/Logs/GaugeCtrl-${shortdate}.log",
                Layout = "${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}",
                ArchiveEvery = FileArchivePeriod.Day,
                MaxArchiveFiles = 7,
                Encoding = System.Text.Encoding.UTF8
            };

            // 控制台目标（调试时使用）
            var consoleTarget = new ConsoleTarget("consoleTarget")
            {
                Layout = "${time} [${level:uppercase=true}] ${logger:shortName=true} - ${message} ${exception:format=tostring}"
            };

            // 添加规则
            config.AddRule(LogLevel.Debug, LogLevel.Fatal, consoleTarget);
            config.AddRule(LogLevel.Info, LogLevel.Fatal, fileTarget);

            LogManager.Configuration = config;
        }

        private void SetupExceptionHandling()
        {
            // 处理UI线程未捕获的异常
            DispatcherUnhandledException += (sender, e) =>
            {
                Logger.Fatal(e.Exception, "UI线程发生未捕获的异常");
                
                var result = MessageBox.Show(
                    $"应用程序发生严重错误：\n{e.Exception.Message}\n\n是否继续运行？",
                    "严重错误",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);
                
                if (result == MessageBoxResult.Yes)
                {
                    e.Handled = true;
                }
            };

            // 处理非UI线程未捕获的异常
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                Logger.Fatal(e.ExceptionObject as Exception, "应用程序域发生未捕获的异常");
                
                if (e.IsTerminating)
                {
                    MessageBox.Show(
                        $"应用程序即将终止：\n{(e.ExceptionObject as Exception)?.Message}",
                        "致命错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            };

            // 处理Task未捕获的异常
            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                Logger.Error(e.Exception, "Task发生未观察到的异常");
                e.SetObserved();
            };
        }
    }
}