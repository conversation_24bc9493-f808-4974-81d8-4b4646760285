using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Data;
using System.Collections.ObjectModel;
using System.Drawing;
using HandyControl.Controls;
using GaugeCtrl.Communication;

namespace GaugeCtrl.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        [ObservableProperty]
        private object _currentView;

        [ObservableProperty]
        private bool _isAnalyzerViewActive;

        private readonly SerialSettingsViewModel _serialSettingsViewModel;
        private readonly AnalyzerViewModel _analyzerViewModel;
        private readonly SerialPortManager _serialPortManager;

        public SerialSettingsViewModel SerialSettingsViewModel => _serialSettingsViewModel;
        public AnalyzerViewModel AnalyzerViewModel => _analyzerViewModel;
        public SerialPortManager SerialPortManager => _serialPortManager;

        public MainWindowViewModel()
        {
            _serialPortManager = new SerialPortManager();
            _serialSettingsViewModel = new SerialSettingsViewModel(_serialPortManager);
            _analyzerViewModel = new AnalyzerViewModel(_serialPortManager);

            // 默认显示串口设置页面
            CurrentView = _serialSettingsViewModel;
            IsAnalyzerViewActive = false;
        }

        [RelayCommand]
        private void SwitchToSerialSettings()
        {
            CurrentView = _serialSettingsViewModel;
            IsAnalyzerViewActive = false;
        }

        [RelayCommand]
        private void SwitchToAnalyzer()
        {
            CurrentView = _analyzerViewModel;
            IsAnalyzerViewActive = true;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            _serialSettingsViewModel?.Cleanup();
        }
    }
}
