<UserControl x:Class="GaugeCtrl.Views.SerialSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             xmlns:vm="clr-namespace:GaugeCtrl.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             d:DataContext="{d:DesignInstance vm:SerialSettingsViewModel}">

    <hc:TabControl Margin="10" TabStripPlacement="Top">

        <!-- 串口配置选项卡 -->
        <hc:TabItem Header="串口配置">
            <ScrollViewer>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- USB2.0串口配置 -->
                    <hc:Card Grid.Column="0" Header="USB2.0总线 - 参数配置和控制">
                        <StackPanel Margin="15">
                            <hc:ComboBox hc:InfoElement.Title="串口号"
                                         ItemsSource="{Binding AvailableUsb2Ports}"
                                         SelectedItem="{Binding SelectedUsb2PortName}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="波特率"
                                         ItemsSource="{Binding BaudRates}"
                                         SelectedItem="{Binding SelectedUsb2BaudRate}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="数据位"
                                         ItemsSource="{Binding DataBits}"
                                         SelectedItem="{Binding SelectedUsb2DataBits}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="奇偶校验"
                                         ItemsSource="{Binding ParityOptions}"
                                         SelectedItem="{Binding SelectedUsb2Parity}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="停止位"
                                         ItemsSource="{Binding StopBitsOptions}"
                                         SelectedItem="{Binding SelectedUsb2StopBits}"
                                         Margin="5" />

                            <!-- USB2.0连接状态显示 -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                                <TextBlock Text="连接状态: " FontWeight="Bold" />
                                <TextBlock FontWeight="Bold">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUsb2Connected}" Value="True">
                                                    <Setter Property="Text" Value="已连接" />
                                                    <Setter Property="Foreground" Value="Green" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsUsb2Connected}" Value="False">
                                                    <Setter Property="Text" Value="未连接" />
                                                    <Setter Property="Foreground" Value="Red" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!-- USB2.0操作按钮 -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                                <Button Content="连接USB2.0"
                                        Command="{Binding ConnectUsb2Command}"
                                        Margin="5">
                                    <Button.Style>
                                        <Style TargetType="Button" BasedOn="{StaticResource ButtonPrimary}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUsb2Connected}" Value="True">
                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>

                                <Button Content="断开USB2.0"
                                        Command="{Binding DisconnectUsb2Command}"
                                        Margin="5">
                                    <Button.Style>
                                        <Style TargetType="Button" BasedOn="{StaticResource ButtonDanger}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUsb2Connected}" Value="False">
                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </hc:Card>

                    <!-- USB3.0串口配置 -->
                    <hc:Card Grid.Column="1" Header="USB3.0总线 - 数据传输">
                        <StackPanel Margin="15">
                            <hc:ComboBox hc:InfoElement.Title="串口号"
                                         ItemsSource="{Binding AvailableUsb3Ports}"
                                         SelectedItem="{Binding SelectedUsb3PortName}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="波特率"
                                         ItemsSource="{Binding BaudRates}"
                                         SelectedItem="{Binding SelectedUsb3BaudRate}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="数据位"
                                         ItemsSource="{Binding DataBits}"
                                         SelectedItem="{Binding SelectedUsb3DataBits}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="奇偶校验"
                                         ItemsSource="{Binding ParityOptions}"
                                         SelectedItem="{Binding SelectedUsb3Parity}"
                                         Margin="5" />

                            <hc:ComboBox hc:InfoElement.Title="停止位"
                                         ItemsSource="{Binding StopBitsOptions}"
                                         SelectedItem="{Binding SelectedUsb3StopBits}"
                                         Margin="5" />

                            <!-- USB3.0连接状态显示 -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                                <TextBlock Text="连接状态: " FontWeight="Bold" />
                                <TextBlock FontWeight="Bold">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUsb3Connected}" Value="True">
                                                    <Setter Property="Text" Value="已连接" />
                                                    <Setter Property="Foreground" Value="Green" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsUsb3Connected}" Value="False">
                                                    <Setter Property="Text" Value="未连接" />
                                                    <Setter Property="Foreground" Value="Red" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!-- USB3.0操作按钮 -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                                <Button Content="连接USB3.0"
                                        Command="{Binding ConnectUsb3Command}"
                                        Margin="5">
                                    <Button.Style>
                                        <Style TargetType="Button" BasedOn="{StaticResource ButtonPrimary}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUsb3Connected}" Value="True">
                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>

                                <Button Content="断开USB3.0"
                                        Command="{Binding DisconnectUsb3Command}"
                                        Margin="5">
                                    <Button.Style>
                                        <Style TargetType="Button" BasedOn="{StaticResource ButtonDanger}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUsb3Connected}" Value="False">
                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </hc:Card>

                    <!-- 操作按钮 -->
                    <hc:Card Grid.Column="2" Header="一键操作">
                        <StackPanel HorizontalAlignment="Center" Margin="10">
                            <Button Content="刷新串口列表"
                                    Command="{Binding RefreshPortsCommand}"
                                    Style="{StaticResource ButtonInfo}"
                                    Margin="10"
                                    MinWidth="120" />

                            <Button Content="连接所有串口"
                                    Command="{Binding ConnectAllCommand}"
                                    Style="{StaticResource ButtonSuccess}"
                                    Margin="10"
                                    MinWidth="120" />

                            <Button Content="断开所有串口"
                                    Command="{Binding DisconnectAllCommand}"
                                    Style="{StaticResource ButtonWarning}"
                                    Margin="10"
                                    MinWidth="120" />
                        </StackPanel>
                    </hc:Card>
                </Grid>
            </ScrollViewer>
        </hc:TabItem>

        <!-- 参数设置选项卡 -->
        <hc:TabItem Header="参数设置">
            <ScrollViewer>
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- 滤波参数设置 -->
                    <hc:Card Grid.Column="0" Grid.Row="0" Header="滤波参数" Margin="5">
                        <StackPanel Margin="15">
                            <hc:ComboBox hc:InfoElement.Title="滤波算法类型"
                                         ItemsSource="{Binding FilterTypes}"
                                         SelectedValue="{Binding SelectedFilterType}"
                                         DisplayMemberPath="Value"
                                         SelectedValuePath="Key"
                                         Margin="5" />

                            <!-- 低通滤波参数 -->
                            <GroupBox Header="低通滤波参数" Margin="5,15,5,5"
                                      Visibility="{Binding IsLowPassVisible, Converter={StaticResource Boolean2VisibilityConverter}}">
                                <StackPanel Margin="10">
                                    <StackPanel Margin="5">
                                        <TextBlock Text="截止频率 (KHz)" Margin="0,0,0,3" />
                                        <hc:NumericUpDown Value="{Binding LowPassCutoffFrequency}"
                                                          Minimum="1"
                                                          Maximum="1000000" />
                                    </StackPanel>

                                    <StackPanel Margin="5">
                                        <TextBlock Text="采样周期 (ms)" Margin="0,0,0,3" />
                                        <hc:NumericUpDown Value="{Binding LowPassSamplingPeriod}"
                                                          Minimum="1"
                                                          Maximum="10000" />
                                    </StackPanel>
                                </StackPanel>
                            </GroupBox>

                            <!-- 高通滤波参数 -->
                            <GroupBox Header="高通滤波参数" Margin="5,15,5,5"
                                      Visibility="{Binding IsHighPassVisible, Converter={StaticResource Boolean2VisibilityConverter}}">
                                <StackPanel Margin="10">
                                    <StackPanel Margin="5">
                                        <TextBlock Text="截止频率 (KHz)" Margin="0,0,0,3" />
                                        <hc:NumericUpDown Value="{Binding HighPassCutoffFrequency}"
                                                          Minimum="1"
                                                          Maximum="1000000" />
                                    </StackPanel>

                                    <StackPanel Margin="5">
                                        <TextBlock Text="采样周期 (ms)" Margin="0,0,0,3" />
                                        <hc:NumericUpDown Value="{Binding HighPassSamplingPeriod}"
                                                          Minimum="1"
                                                          Maximum="10000" />
                                    </StackPanel>
                                </StackPanel>
                            </GroupBox>

                            <!-- 滑动平均参数 -->
                            <GroupBox Header="滑动平均参数" Margin="5,15,5,5"
                                      Visibility="{Binding IsMovingAverageVisible, Converter={StaticResource Boolean2VisibilityConverter}}">
                                <StackPanel Margin="10">
                                    <StackPanel Margin="5">
                                        <TextBlock Text="窗口宽度" Margin="0,0,0,3" />
                                        <hc:NumericUpDown Value="{Binding MovingAverageWindowWidth}"
                                                          Minimum="1"
                                                          Maximum="1000" />
                                    </StackPanel>
                                </StackPanel>
                            </GroupBox>

                            <Button Content="设置滤波参数"
                                    Command="{Binding SetFilterParameterCommand}"
                                    Style="{StaticResource ButtonPrimary}"
                                    Margin="5,20,5,5"
                                    HorizontalAlignment="Center"
                                    MinWidth="120" />
                        </StackPanel>
                    </hc:Card>

                    <!-- 信号时延设置 -->
                    <hc:Card Grid.Column="1" Grid.Row="0" Header="信号时延" Margin="5">
                        <StackPanel Margin="15">
                            <StackPanel Margin="5">
                                <TextBlock Text="信号延迟时长 (ms)" Margin="0,0,0,3" />
                                <hc:NumericUpDown Value="{Binding SignalDelayTime}"
                                                  Minimum="0"
                                                  Maximum="10000" />
                            </StackPanel>

                            <TextBlock Text="说明：延迟时长为不同信号之间延迟的最大时长，用于信号同步处理。"
                                       TextWrapping="Wrap"
                                       FontSize="12"
                                       Foreground="Gray"
                                       Margin="5,15"
                                       TextAlignment="Center" />

                            <Button Content="设置时延参数"
                                    Command="{Binding SetDelayParameterCommand}"
                                    Style="{StaticResource ButtonPrimary}"
                                    Margin="5,20,5,5"
                                    HorizontalAlignment="Center"
                                    MinWidth="120" />
                        </StackPanel>
                    </hc:Card>

                    <!-- 示波器设置 -->
                    <hc:Card Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2" Header="示波器设置" Margin="5">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <!-- 统一的示波器配置列表 -->
                            <DataGrid Grid.Row="0"
                                      ItemsSource="{Binding OscilloscopeChannels}"
                                      AutoGenerateColumns="False"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      SelectionMode="Single">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="通道"
                                                        Binding="{Binding ChannelName}"
                                                        IsReadOnly="True"
                                                        Width="80" />

                                    <DataGridTemplateColumn Header="数据类型" Width="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <hc:ComboBox
                                                    ItemsSource="{Binding Path=DataContext.OscilloscopeDataTypes, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    SelectedValue="{Binding DataType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    DisplayMemberPath="Value"
                                                    SelectedValuePath="Key"
                                                    Margin="2" />
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <!-- <DataGridTemplateColumn Header="增益调节 (mv)" Width="120"> -->
                                    <!--     <DataGridTemplateColumn.CellTemplate> -->
                                    <!--         <DataTemplate> -->
                                    <!--             <hc:NumericUpDown Value="{Binding GainAdjustment, Mode=TwoWay}" -->
                                    <!--                               Minimum="0" -->
                                    <!--                               Maximum="65535" -->
                                    <!--                               Margin="2" /> -->
                                    <!--         </DataTemplate> -->
                                    <!--     </DataGridTemplateColumn.CellTemplate> -->
                                    <!-- </DataGridTemplateColumn> -->
                                    <!-- -->
                                    <!-- <DataGridTemplateColumn Header="基线调节 (mv)" Width="120"> -->
                                    <!--     <DataGridTemplateColumn.CellTemplate> -->
                                    <!--         <DataTemplate> -->
                                    <!--             <hc:NumericUpDown Value="{Binding BaselineAdjustment, Mode=TwoWay}" -->
                                    <!--                               Minimum="0" -->
                                    <!--                               Maximum="65535" -->
                                    <!--                               Margin="2" /> -->
                                    <!--         </DataTemplate> -->
                                    <!--     </DataGridTemplateColumn.CellTemplate> -->
                                    <!-- </DataGridTemplateColumn> -->
                                    <!-- -->
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- 操作按钮 -->
                            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                                <Button Content="应用示波器设置"
                                        Command="{Binding SetOscilloscopeParameterCommand}"
                                        Style="{StaticResource ButtonPrimary}"
                                        Margin="5"
                                        MinWidth="120" />

                                <!-- <Button Content="应用系统参数" -->
                                <!--         Command="{Binding SetSystemParameterCommand}" -->
                                <!--         Style="{StaticResource ButtonSuccess}" -->
                                <!--         Margin="5" -->
                                <!--         MinWidth="120" /> -->
                                <!-- -->
                                <!-- <Button Content="重置通道配置" -->
                                <!--         Command="{Binding ResetOscilloscopeChannelsCommand}" -->
                                <!--         Style="{StaticResource ButtonWarning}" -->
                                <!--         Margin="5" -->
                                <!--         MinWidth="120" /> -->
                            </StackPanel>
                        </Grid>
                    </hc:Card>
                </Grid>
            </ScrollViewer>
        </hc:TabItem>

    </hc:TabControl>
</UserControl>