<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>OscilloscopeApp</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="CompiledBindings.WPF" Version="1.0.18" />
    <PackageReference Include="HandyControl" Version="3.5.1" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
    <PackageReference Include="NLog" Version="6.0.1" />
    <PackageReference Include="NLog.Extensions.Logging" Version="6.0.1" />
    <PackageReference Include="ScottPlot.WPF" Version="5.0.55" />
    <PackageReference Include="TouchSocket" Version="3.1.16" />
    <PackageReference Include="ValueConverters" Version="4.0.6-pre" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GaugeCtrl.Core\GaugeCtrl.Core.csproj" />
    <ProjectReference Include="..\GaugeCtrl.Communication\GaugeCtrl.Communication.csproj" />
  </ItemGroup>





</Project>