using System;
using HandyControl.Controls;
using HandyControl.Data;
using GaugeCtrl.Views;
using GaugeCtrl.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace GaugeCtrl
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : System.Windows.Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainWindowViewModel();
        }

        public MainWindowViewModel ViewModel => (MainWindowViewModel)DataContext;
    }
}
